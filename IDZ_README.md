# IDZ独立怪物系列 - 使用指南

## 系统简介

IDZ (Independent DeathZombie) 是一个完全独立的自定义怪物系列，基于DeathZombieV4插件开发。IDZ系列提供了强大的自定义怪物创建功能，支持技能继承、粒子效果、奖励系统等高级特性。

### 核心特点

- **独立系列**: IDZ使用独立的元数据标记系统，不依赖现有的UserCustomEntity
- **技能继承**: 可以继承ID系列(35个)和IDC系列(50个)共85个技能模板
- **粒子效果**: 支持多种粒子动画类型（圆形、螺旋、波浪、爆炸等）
- **完全自定义**: 支持自定义实体类型、属性、装备、技能参数
- **奖励系统**: 支持金钱、经验、物品、命令等多种奖励类型
- **可视化编辑**: 计划支持WebUI可视化编辑器

## 快速开始

### 1. 创建你的第一个IDZ怪物

```bash
# 创建一个基础的IDZ僵尸
/czm make 我的超级僵尸 ZOMBIE

# 创建一个IDZ蜘蛛
/czm make 毒液蜘蛛 SPIDER

# 创建一个IDZ烈焰人
/czm make 火焰法师 BLAZE
```

### 2. 查看和管理IDZ怪物

```bash
# 查看所有IDZ怪物列表
/czm list

# 查看特定怪物信息
/czm edit 我的超级僵尸

# 生成IDZ怪物
/czm spawn 我的超级僵尸

# 删除IDZ怪物
/czm delete 我的超级僵尸
```

### 3. 系统管理

```bash
# 查看系统状态
/czm status

# 运行快速测试
/czm test quick

# 运行完整测试（需要玩家执行）
/czm test full

# 重新加载配置
/czm reload
```

## 配置文件详解

IDZ系统使用 `Custom.yml` 配置文件存储所有自定义怪物数据。

### 基础配置结构

```yaml
custom_monsters:
  "我的超级僵尸":
    id: "idz1"
    display_name: "§c我的超级僵尸"
    entity_type: "ZOMBIE"
    description: "这是一个自定义的IDZ怪物"
    health: 200.0
    damage: 15.0
    speed: 0.3
    enabled: true
    
    # 继承技能
    inherited_skills:
      - "id5_poison_attack"    # ID5的剧毒攻击
      - "idc3_flame_attack"    # IDC3的烈焰攻击
    
    # 技能参数
    skill_parameters:
      poison_level: 2
      poison_duration: 5000
      flame_damage: 8.0
    
    # 装备配置
    equipment:
      weapon: "DIAMOND_SWORD"
      helmet: "DIAMOND_HELMET"
      enchantments:
        SHARPNESS: 3
        PROTECTION: 2
    
    # 粒子效果
    particle_effects:
      spawn:
        type: "FLAME"
        count: 50
        spread: 2.0
        duration: 3000
      attack:
        type: "CRIT"
        count: 20
        spread: 1.0
    
    # 奖励配置
    rewards:
      money: 100.0
      experience: 50
      random_money: true
      money_min: 80.0
      money_max: 120.0

# 全局设置
settings:
  max_custom_monsters: 100
  enable_particle_effects: true
  debug_mode: false
```

## 技能系统

### 可继承的技能模板

#### ID系列技能 (35个)
**基础技能 (ID5-ID17)**:
- `id5_poison_attack` - 剧毒攻击
- `id6_death_summon` - 死亡召唤
- `id7_arrow_attack` - 箭矢攻击
- `id10_mage_summon` - 法师召唤
- `id11_explode_attack` - 自爆攻击
- `id12_poison_arrow` - 毒箭攻击
- `id13_lightning_attack` - 电击攻击
- `id14_freeze_attack` - 冰冻攻击
- `id15_teleport_attack` - 传送攻击
- `id17_thunder_attack` - 雷霆攻击

**高级技能 (ID18-ID25)**:
- `id18_damage_summon` - 受伤召唤 (变异科学家)
- `id18_area_debuff` - 范围减益
- `id18_global_damage` - 全局伤害
- `id19_area_lightning` - 范围闪电 (变异法师)
- `id20_flight` - 飞行效果 (气球僵尸)
- `id21_fog_generation` - 迷雾生成 (迷雾僵尸)
- `id22_global_lightning` - 全局雷电 (变异雷霆僵尸)
- `id22_time_control` - 时间控制
- `id25_random_summon` - 随机召唤 (终极变异体)
- ... (共35个技能)

#### IDC系列技能 (50个)
**基础变异技能 (IDC1-IDC9)**:
- `idc1_enhanced_poison` - 增强剧毒攻击
- `idc3_flame_attack` - 烈焰攻击
- `idc4_charged_lightning` - 充能闪电攻击
- `idc5_area_electric` - 范围电流
- `idc6_web_trap` - 蜘蛛网陷阱
- `idc7_summon_vindicators` - 召唤卫道士
- `idc8_dna_spiral` - DNA螺旋魔法
- `idc9_crit_aura` - 暴击光环

**高级变异技能 (IDC10-IDC22)**:
- `idc10_charge_damage` - 撞击伤害 (变异僵尸马)
- `idc15_red_particle_ball` - 红色粒子球 (鲜血猪灵)
- `idc20_soul_drain` - 灵魂汲取 (灵魂坚守者)
- `idc21_wither_skull` - 凋零头颅 (凋零领主)
- `idc22_obsidian_pillar` - 黑曜石柱 (异变之王)
- `idc22_crystal_attack` - 末影水晶攻击
- `idc22_boss_bar` - Boss血条
- ... (共50个技能)

### 技能参数配置

每个技能都支持自定义参数：

```yaml
skill_parameters:
  # 剧毒攻击参数
  poison_level: 2          # 毒性等级
  poison_duration: 5000    # 持续时间(毫秒)
  poison_chance: 1.0       # 触发概率
  
  # 烈焰攻击参数
  flame_damage: 8.0        # 火焰伤害
  flame_interval: 8000     # 攻击间隔
  flame_range: 12.0        # 攻击范围
  
  # 召唤技能参数
  summon_count: 2          # 召唤数量
  summon_type: "ZOMBIE"    # 召唤类型
  summon_radius: 3.0       # 召唤范围
```

## 粒子效果系统

### 支持的粒子类型

- `FLAME` - 火焰粒子
- `SMOKE_NORMAL` - 烟雾粒子
- `EXPLOSION_LARGE` - 大爆炸粒子
- `CRIT` - 暴击粒子
- `ENCHANTMENT_TABLE` - 附魔台粒子
- `REDSTONE` - 红石粒子（支持自定义颜色）
- ... (所有Bukkit粒子类型)

### 动画类型

- `STATIC` - 静态粒子
- `CIRCLE` - 圆形动画
- `SPIRAL` - 螺旋动画
- `WAVE` - 波浪动画
- `EXPLOSION` - 爆炸动画
- `BEAM` - 光束动画
- `ORBIT` - 轨道动画

### 触发条件

- `spawn` - 生成时
- `attack` - 攻击时
- `hurt` - 受伤时
- `death` - 死亡时
- `move` - 移动时
- `idle` - 空闲时
- `skill_cast` - 技能释放时

## 元数据标记系统

IDZ怪物使用独立的元数据标记系统：

### 核心标记
- `idzCustomMonster: true` - IDZ怪物标识
- `monsterSeries: "IDZ"` - 怪物系列标记
- `idzMonsterId: "idz1"` - 怪物ID
- `idzMonsterName: "我的超级僵尸"` - 怪物名称

### 游戏识别标记
- `gameEntity: true` - 游戏实体标识
- `customGameMonster: true` - 自定义游戏怪物标识

### 功能标记
- `idzHasSkills: true` - 拥有技能标记
- `idzSkillCount: 3` - 技能数量
- `idzHasRewards: true` - 拥有奖励标记

## 权限系统

### 基础权限
- `deathzombie.idz.make` - 创建IDZ怪物
- `deathzombie.idz.edit` - 编辑IDZ怪物
- `deathzombie.idz.delete` - 删除IDZ怪物
- `deathzombie.idz.list` - 查看IDZ怪物列表
- `deathzombie.idz.gui` - 使用GUI编辑器

### 管理权限
- `deathzombie.admin` - 系统管理权限（测试、状态、重载）

## 故障排除

### 常见问题

1. **IDZ系统未初始化**
   - 检查插件是否正确加载
   - 查看控制台错误信息
   - 运行 `/czm test quick` 进行诊断

2. **怪物无法生成**
   - 确认怪物配置有效 (`/czm edit <名称>`)
   - 检查怪物是否启用
   - 验证实体类型是否支持

3. **技能不生效**
   - 检查技能ID是否正确
   - 验证技能参数配置
   - 查看控制台技能执行日志

4. **粒子效果不显示**
   - 确认粒子效果已启用 (`enable_particle_effects: true`)
   - 检查粒子类型是否有效
   - 验证客户端粒子设置

### 调试模式

启用调试模式获取详细日志：

```yaml
settings:
  debug_mode: true
```

### 系统测试

运行系统测试来诊断问题：

```bash
# 快速测试（基础功能）
/czm test quick

# 完整测试（包括实体生成）
/czm test full

# 查看系统状态
/czm status
```

## 开发计划

### 即将推出的功能

1. **WebUI可视化编辑器**
   - 拖拽式技能配置
   - 实时粒子效果预览
   - 可视化属性编辑

2. **高级技能系统**
   - 自定义技能脚本
   - 技能组合效果
   - 条件触发系统

3. **模板系统**
   - 怪物模板保存和分享
   - 预设怪物包
   - 社区模板库

4. **性能优化**
   - 异步处理
   - 内存优化
   - 批量操作

## 技术支持

如果遇到问题或需要帮助，请：

1. 查看控制台日志
2. 运行系统测试 (`/czm test`)
3. 检查配置文件格式
4. 联系插件开发者

---

**IDZ独立怪物系列 v1.0**  
*Independent DeathZombie Series - 让创造力无限延伸*
