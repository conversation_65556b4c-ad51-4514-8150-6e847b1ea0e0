package org.Ver_zhzh.customZombie.UserMaker;

import org.bukkit.Color;
import org.bukkit.Location;
import org.bukkit.Particle;
import org.bukkit.entity.Player;

import java.util.HashMap;
import java.util.Map;

/**
 * 粒子效果配置类
 * 用于定义和管理自定义怪物的粒子效果
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class ParticleEffect {
    
    // 粒子类型
    private Particle particleType;
    
    // 基础属性
    private int count;                    // 粒子数量
    private double offsetX;               // X轴偏移
    private double offsetY;               // Y轴偏移
    private double offsetZ;               // Z轴偏移
    private double extra;                 // 额外数据（速度等）
    
    // 高级属性
    private double spread;                // 扩散范围
    private long duration;                // 持续时间（毫秒）
    private long interval;                // 播放间隔（毫秒）
    private boolean force;                // 是否强制显示
    private double range;                 // 可见范围
    
    // 颜色配置（用于支持颜色的粒子）
    private Color color;
    private float size;                   // 粒子大小
    
    // 触发配置
    private String trigger;               // 触发条件
    private Map<String, Object> triggerData; // 触发数据
    
    // 动画配置
    private AnimationType animationType;  // 动画类型
    private Map<String, Object> animationData; // 动画数据
    
    // 音效配置
    private String sound;                 // 音效名称
    private float soundVolume;            // 音效音量
    private float soundPitch;             // 音效音调
    
    /**
     * 动画类型枚举
     */
    public enum AnimationType {
        STATIC,         // 静态
        CIRCLE,         // 圆形
        SPIRAL,         // 螺旋
        WAVE,           // 波浪
        EXPLOSION,      // 爆炸
        BEAM,           // 光束
        ORBIT,          // 轨道
        CUSTOM          // 自定义
    }
    
    /**
     * 触发条件枚举
     */
    public enum TriggerType {
        SPAWN,          // 生成时
        ATTACK,         // 攻击时
        HURT,           // 受伤时
        DEATH,          // 死亡时
        MOVE,           // 移动时
        IDLE,           // 空闲时
        SKILL_CAST,     // 技能释放时
        CUSTOM          // 自定义
    }
    
    /**
     * 默认构造函数
     */
    public ParticleEffect() {
        this.particleType = Particle.FLAME;
        this.count = 10;
        this.offsetX = 0.5;
        this.offsetY = 0.5;
        this.offsetZ = 0.5;
        this.extra = 0.1;
        this.spread = 1.0;
        this.duration = 1000;
        this.interval = 100;
        this.force = false;
        this.range = 32.0;
        this.size = 1.0f;
        this.soundVolume = 1.0f;
        this.soundPitch = 1.0f;
        this.animationType = AnimationType.STATIC;
        this.triggerData = new HashMap<>();
        this.animationData = new HashMap<>();
    }
    
    /**
     * 构造函数
     * 
     * @param particleType 粒子类型
     * @param count 粒子数量
     * @param spread 扩散范围
     */
    public ParticleEffect(Particle particleType, int count, double spread) {
        this();
        this.particleType = particleType;
        this.count = count;
        this.spread = spread;
    }
    
    /**
     * 在指定位置播放粒子效果
     * 
     * @param location 位置
     */
    public void play(Location location) {
        if (location == null || location.getWorld() == null) {
            return;
        }
        
        try {
            // 播放粒子效果
            switch (animationType) {
                case STATIC:
                    playStaticEffect(location);
                    break;
                case CIRCLE:
                    playCircleEffect(location);
                    break;
                case SPIRAL:
                    playSpiralEffect(location);
                    break;
                case WAVE:
                    playWaveEffect(location);
                    break;
                case EXPLOSION:
                    playExplosionEffect(location);
                    break;
                case BEAM:
                    playBeamEffect(location);
                    break;
                case ORBIT:
                    playOrbitEffect(location);
                    break;
                default:
                    playStaticEffect(location);
                    break;
            }
            
            // 播放音效
            if (sound != null && !sound.isEmpty()) {
                try {
                    location.getWorld().playSound(location, 
                        org.bukkit.Sound.valueOf(sound.toUpperCase()), 
                        soundVolume, soundPitch);
                } catch (IllegalArgumentException e) {
                    // 忽略无效的音效名称
                }
            }
            
        } catch (Exception e) {
            // 忽略粒子播放错误，避免影响游戏
        }
    }
    
    /**
     * 播放静态粒子效果
     */
    private void playStaticEffect(Location location) {
        if (color != null && particleType == Particle.REDSTONE) {
            // 彩色粒子
            Particle.DustOptions dustOptions = new Particle.DustOptions(color, size);
            location.getWorld().spawnParticle(particleType, location, count, 
                offsetX, offsetY, offsetZ, extra, dustOptions, force);
        } else {
            // 普通粒子
            location.getWorld().spawnParticle(particleType, location, count, 
                offsetX, offsetY, offsetZ, extra, null, force);
        }
    }
    
    /**
     * 播放圆形粒子效果
     */
    private void playCircleEffect(Location location) {
        double radius = animationData.getOrDefault("radius", 2.0);
        int points = (int) animationData.getOrDefault("points", 20);
        
        for (int i = 0; i < points; i++) {
            double angle = 2 * Math.PI * i / points;
            double x = radius * Math.cos(angle);
            double z = radius * Math.sin(angle);
            
            Location particleLocation = location.clone().add(x, 0, z);
            location.getWorld().spawnParticle(particleType, particleLocation, 1, 
                0, 0, 0, 0, null, force);
        }
    }
    
    /**
     * 播放螺旋粒子效果
     */
    private void playSpiralEffect(Location location) {
        double radius = (Double) animationData.getOrDefault("radius", 2.0);
        double height = (Double) animationData.getOrDefault("height", 3.0);
        int points = (Integer) animationData.getOrDefault("points", 50);
        
        for (int i = 0; i < points; i++) {
            double angle = 4 * Math.PI * i / points;
            double currentRadius = radius * (1 - (double) i / points);
            double y = height * i / points;
            
            double x = currentRadius * Math.cos(angle);
            double z = currentRadius * Math.sin(angle);
            
            Location particleLocation = location.clone().add(x, y, z);
            location.getWorld().spawnParticle(particleType, particleLocation, 1, 
                0, 0, 0, 0, null, force);
        }
    }
    
    /**
     * 播放波浪粒子效果
     */
    private void playWaveEffect(Location location) {
        double amplitude = (Double) animationData.getOrDefault("amplitude", 1.0);
        double frequency = (Double) animationData.getOrDefault("frequency", 2.0);
        int points = (Integer) animationData.getOrDefault("points", 30);
        
        for (int i = 0; i < points; i++) {
            double x = (double) i / points * 4 - 2; // -2 到 2
            double y = amplitude * Math.sin(frequency * x);
            
            Location particleLocation = location.clone().add(x, y, 0);
            location.getWorld().spawnParticle(particleType, particleLocation, 1, 
                0, 0, 0, 0, null, force);
        }
    }
    
    /**
     * 播放爆炸粒子效果
     */
    private void playExplosionEffect(Location location) {
        double maxRadius = (Double) animationData.getOrDefault("maxRadius", 3.0);
        int layers = (Integer) animationData.getOrDefault("layers", 5);
        
        for (int layer = 0; layer < layers; layer++) {
            double radius = maxRadius * (layer + 1) / layers;
            int particlesInLayer = (layer + 1) * 8;
            
            for (int i = 0; i < particlesInLayer; i++) {
                double angle = 2 * Math.PI * i / particlesInLayer;
                double x = radius * Math.cos(angle);
                double z = radius * Math.sin(angle);
                double y = (Math.random() - 0.5) * 2;
                
                Location particleLocation = location.clone().add(x, y, z);
                location.getWorld().spawnParticle(particleType, particleLocation, 1, 
                    0, 0, 0, 0.1, null, force);
            }
        }
    }
    
    /**
     * 播放光束粒子效果
     */
    private void playBeamEffect(Location location) {
        double height = (Double) animationData.getOrDefault("height", 5.0);
        int points = (Integer) animationData.getOrDefault("points", 20);
        
        for (int i = 0; i <= points; i++) {
            double y = height * i / points;
            Location particleLocation = location.clone().add(0, y, 0);
            location.getWorld().spawnParticle(particleType, particleLocation, 1, 
                0.1, 0, 0.1, 0, null, force);
        }
    }
    
    /**
     * 播放轨道粒子效果
     */
    private void playOrbitEffect(Location location) {
        double radius = (Double) animationData.getOrDefault("radius", 2.0);
        int orbits = (Integer) animationData.getOrDefault("orbits", 3);
        int pointsPerOrbit = (Integer) animationData.getOrDefault("pointsPerOrbit", 10);
        
        for (int orbit = 0; orbit < orbits; orbit++) {
            double orbitAngle = 2 * Math.PI * orbit / orbits;
            
            for (int i = 0; i < pointsPerOrbit; i++) {
                double angle = 2 * Math.PI * i / pointsPerOrbit;
                double x = radius * Math.cos(angle) * Math.cos(orbitAngle);
                double y = radius * Math.sin(angle);
                double z = radius * Math.cos(angle) * Math.sin(orbitAngle);
                
                Location particleLocation = location.clone().add(x, y, z);
                location.getWorld().spawnParticle(particleType, particleLocation, 1, 
                    0, 0, 0, 0, null, force);
            }
        }
    }
    
    // Getter和Setter方法
    public Particle getParticleType() { return particleType; }
    public void setParticleType(Particle particleType) { this.particleType = particleType; }
    
    public int getCount() { return count; }
    public void setCount(int count) { this.count = count; }
    
    public double getOffsetX() { return offsetX; }
    public void setOffsetX(double offsetX) { this.offsetX = offsetX; }
    
    public double getOffsetY() { return offsetY; }
    public void setOffsetY(double offsetY) { this.offsetY = offsetY; }
    
    public double getOffsetZ() { return offsetZ; }
    public void setOffsetZ(double offsetZ) { this.offsetZ = offsetZ; }
    
    public double getExtra() { return extra; }
    public void setExtra(double extra) { this.extra = extra; }
    
    public double getSpread() { return spread; }
    public void setSpread(double spread) { this.spread = spread; }
    
    public long getDuration() { return duration; }
    public void setDuration(long duration) { this.duration = duration; }
    
    public long getInterval() { return interval; }
    public void setInterval(long interval) { this.interval = interval; }
    
    public boolean isForce() { return force; }
    public void setForce(boolean force) { this.force = force; }
    
    public double getRange() { return range; }
    public void setRange(double range) { this.range = range; }
    
    public Color getColor() { return color; }
    public void setColor(Color color) { this.color = color; }
    
    public float getSize() { return size; }
    public void setSize(float size) { this.size = size; }
    
    public String getTrigger() { return trigger; }
    public void setTrigger(String trigger) { this.trigger = trigger; }
    
    public Map<String, Object> getTriggerData() { return triggerData; }
    public void setTriggerData(Map<String, Object> triggerData) { this.triggerData = triggerData; }
    
    public AnimationType getAnimationType() { return animationType; }
    public void setAnimationType(AnimationType animationType) { this.animationType = animationType; }
    
    public Map<String, Object> getAnimationData() { return animationData; }
    public void setAnimationData(Map<String, Object> animationData) { this.animationData = animationData; }
    
    public String getSound() { return sound; }
    public void setSound(String sound) { this.sound = sound; }
    
    public float getSoundVolume() { return soundVolume; }
    public void setSoundVolume(float soundVolume) { this.soundVolume = soundVolume; }
    
    public float getSoundPitch() { return soundPitch; }
    public void setSoundPitch(float soundPitch) { this.soundPitch = soundPitch; }
}
