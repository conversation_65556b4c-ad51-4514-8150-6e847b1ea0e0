package org.Ver_zhzh.customZombie.UserMaker;

import org.bukkit.Location;
import org.bukkit.attribute.Attribute;
import org.bukkit.entity.LivingEntity;
import org.bukkit.inventory.EntityEquipment;
import org.bukkit.inventory.ItemStack;
import org.bukkit.metadata.FixedMetadataValue;
import org.bukkit.plugin.Plugin;
import org.bukkit.potion.PotionEffect;

import java.util.HashMap;
import java.util.Map;
import java.util.logging.Logger;

/**
 * IDZ实体管理器
 * 负责生成和管理自定义怪物实体
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class IDZEntityManager {
    
    private final Plugin plugin;
    private final Logger logger;
    private final SkillTemplateManager skillManager;
    
    // 活跃的自定义怪物实体
    private final Map<LivingEntity, CustomMonster> activeMonsters;
    
    // 统计信息
    private int totalSpawned = 0;
    private int currentActive = 0;
    
    /**
     * 构造函数
     * 
     * @param plugin 插件实例
     * @param skillManager 技能模板管理器
     */
    public IDZEntityManager(Plugin plugin, SkillTemplateManager skillManager) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.skillManager = skillManager;
        this.activeMonsters = new HashMap<>();
    }
    
    /**
     * 生成自定义怪物
     * 
     * @param location 生成位置
     * @param monster 自定义怪物配置
     * @return 是否生成成功
     */
    public boolean spawnCustomMonster(Location location, CustomMonster monster) {
        if (location == null || monster == null) {
            return false;
        }
        
        if (!monster.isEnabled()) {
            logger.warning("尝试生成已禁用的自定义怪物: " + monster.getName());
            return false;
        }
        
        // 验证怪物配置
        ValidationResult validation = monster.validate();
        if (!validation.isValid()) {
            logger.warning("自定义怪物配置无效: " + monster.getName() + " - " + validation.getFormattedErrors());
            return false;
        }
        
        try {
            // 生成基础实体
            LivingEntity entity = (LivingEntity) location.getWorld().spawnEntity(location, monster.getEntityType());
            
            // 应用基础属性
            applyBasicAttributes(entity, monster);
            
            // 设置装备
            applyEquipment(entity, monster);
            
            // 应用药水效果
            applyPotionEffects(entity, monster);
            
            // 设置元数据标记
            applyMetadata(entity, monster);
            
            // 启用技能
            enableSkills(entity, monster);
            
            // 播放生成粒子效果
            playSpawnEffects(entity, monster);
            
            // 注册到活跃怪物列表
            activeMonsters.put(entity, monster);
            totalSpawned++;
            currentActive++;
            
            logger.info("成功生成自定义怪物: " + monster.getName() + " (" + monster.getId() + ") 在 " + 
                       location.getWorld().getName() + " " + location.getBlockX() + "," + location.getBlockY() + "," + location.getBlockZ());
            
            return true;
            
        } catch (Exception e) {
            logger.severe("生成自定义怪物失败: " + monster.getName() + " - " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 应用基础属性
     */
    private void applyBasicAttributes(LivingEntity entity, CustomMonster monster) {
        try {
            // 设置生命值
            if (entity.getAttribute(Attribute.GENERIC_MAX_HEALTH) != null) {
                entity.getAttribute(Attribute.GENERIC_MAX_HEALTH).setBaseValue(monster.getHealth());
                entity.setHealth(monster.getHealth());
            }
            
            // 设置攻击力
            if (entity.getAttribute(Attribute.GENERIC_ATTACK_DAMAGE) != null) {
                entity.getAttribute(Attribute.GENERIC_ATTACK_DAMAGE).setBaseValue(monster.getDamage());
            }
            
            // 设置移动速度
            if (entity.getAttribute(Attribute.GENERIC_MOVEMENT_SPEED) != null) {
                entity.getAttribute(Attribute.GENERIC_MOVEMENT_SPEED).setBaseValue(monster.getSpeed());
            }
            
            // 设置护甲值
            if (entity.getAttribute(Attribute.GENERIC_ARMOR) != null) {
                entity.getAttribute(Attribute.GENERIC_ARMOR).setBaseValue(monster.getArmor());
            }
            
            // 设置护甲韧性
            if (entity.getAttribute(Attribute.GENERIC_ARMOR_TOUGHNESS) != null) {
                entity.getAttribute(Attribute.GENERIC_ARMOR_TOUGHNESS).setBaseValue(monster.getArmorToughness());
            }
            
            // 设置击退抗性
            if (entity.getAttribute(Attribute.GENERIC_KNOCKBACK_RESISTANCE) != null) {
                entity.getAttribute(Attribute.GENERIC_KNOCKBACK_RESISTANCE).setBaseValue(monster.getKnockbackResistance());
            }
            
            // 设置自定义名称
            if (monster.getDisplayName() != null && !monster.getDisplayName().isEmpty()) {
                entity.setCustomName(monster.getDisplayName());
                entity.setCustomNameVisible(false); // 默认隐藏，由其他系统控制显示
            }
            
        } catch (Exception e) {
            logger.warning("应用基础属性失败: " + e.getMessage());
        }
    }
    
    /**
     * 应用装备
     */
    private void applyEquipment(LivingEntity entity, CustomMonster monster) {
        try {
            EntityEquipment equipment = entity.getEquipment();
            if (equipment == null) {
                return;
            }
            
            // 设置武器
            if (monster.getWeapon() != null) {
                ItemStack weapon = new ItemStack(monster.getWeapon());
                
                // 添加武器附魔
                for (Map.Entry<String, Integer> entry : monster.getWeaponEnchantments().entrySet()) {
                    try {
                        var enchantment = org.bukkit.enchantments.Enchantment.getByName(entry.getKey());
                        if (enchantment != null) {
                            weapon.addUnsafeEnchantment(enchantment, entry.getValue());
                        }
                    } catch (Exception e) {
                        logger.warning("添加武器附魔失败: " + entry.getKey());
                    }
                }
                
                equipment.setItemInMainHand(weapon);
                equipment.setItemInMainHandDropChance(0.0f); // 不掉落
            }
            
            // 设置护甲
            if (monster.getHelmet() != null) {
                ItemStack helmet = new ItemStack(monster.getHelmet());
                addArmorEnchantments(helmet, monster.getArmorEnchantments());
                equipment.setHelmet(helmet);
                equipment.setHelmetDropChance(0.0f);
            }
            
            if (monster.getChestplate() != null) {
                ItemStack chestplate = new ItemStack(monster.getChestplate());
                addArmorEnchantments(chestplate, monster.getArmorEnchantments());
                equipment.setChestplate(chestplate);
                equipment.setChestplateDropChance(0.0f);
            }
            
            if (monster.getLeggings() != null) {
                ItemStack leggings = new ItemStack(monster.getLeggings());
                addArmorEnchantments(leggings, monster.getArmorEnchantments());
                equipment.setLeggings(leggings);
                equipment.setLeggingsDropChance(0.0f);
            }
            
            if (monster.getBoots() != null) {
                ItemStack boots = new ItemStack(monster.getBoots());
                addArmorEnchantments(boots, monster.getArmorEnchantments());
                equipment.setBoots(boots);
                equipment.setBootsDropChance(0.0f);
            }
            
        } catch (Exception e) {
            logger.warning("应用装备失败: " + e.getMessage());
        }
    }
    
    /**
     * 添加护甲附魔
     */
    private void addArmorEnchantments(ItemStack item, Map<String, Integer> enchantments) {
        for (Map.Entry<String, Integer> entry : enchantments.entrySet()) {
            try {
                var enchantment = org.bukkit.enchantments.Enchantment.getByName(entry.getKey());
                if (enchantment != null) {
                    item.addUnsafeEnchantment(enchantment, entry.getValue());
                }
            } catch (Exception e) {
                logger.warning("添加护甲附魔失败: " + entry.getKey());
            }
        }
    }
    
    /**
     * 应用药水效果
     */
    private void applyPotionEffects(LivingEntity entity, CustomMonster monster) {
        try {
            for (PotionEffectConfig effectConfig : monster.getPotionEffects().values()) {
                PotionEffect effect = effectConfig.toPotionEffect();
                if (effect != null) {
                    entity.addPotionEffect(effect);
                }
            }
        } catch (Exception e) {
            logger.warning("应用药水效果失败: " + e.getMessage());
        }
    }
    
    /**
     * 设置元数据标记
     */
    private void applyMetadata(LivingEntity entity, CustomMonster monster) {
        try {
            // IDZ系统独立标记
            entity.setMetadata("idzCustomMonster", new FixedMetadataValue(plugin, true));
            entity.setMetadata("idzMonsterId", new FixedMetadataValue(plugin, monster.getId()));
            entity.setMetadata("idzMonsterName", new FixedMetadataValue(plugin, monster.getName()));
            entity.setMetadata("idzEntityType", new FixedMetadataValue(plugin, monster.getEntityType().name()));

            // 游戏识别标记（确保被游戏系统识别为怪物）
            entity.setMetadata("gameEntity", new FixedMetadataValue(plugin, true));
            entity.setMetadata("customGameMonster", new FixedMetadataValue(plugin, true));

            // IDZ系列标识
            entity.setMetadata("monsterSeries", new FixedMetadataValue(plugin, "IDZ"));
            entity.setMetadata("entityId", new FixedMetadataValue(plugin, monster.getId()));

            // 创建者和时间信息
            entity.setMetadata("idzCreator", new FixedMetadataValue(plugin, monster.getCreator()));
            entity.setMetadata("idzCreateTime", new FixedMetadataValue(plugin, monster.getCreateTime()));
            entity.setMetadata("idzLastModified", new FixedMetadataValue(plugin, monster.getLastModified()));

            // 技能相关标记
            if (!monster.getInheritedSkills().isEmpty()) {
                entity.setMetadata("idzHasSkills", new FixedMetadataValue(plugin, true));
                entity.setMetadata("idzSkillCount", new FixedMetadataValue(plugin, monster.getInheritedSkills().size()));
            }

            // 奖励相关标记
            if (monster.getRewards() != null) {
                entity.setMetadata("idzHasRewards", new FixedMetadataValue(plugin, true));
            }

        } catch (Exception e) {
            logger.warning("设置元数据失败: " + e.getMessage());
        }
    }
    
    /**
     * 启用技能
     */
    private void enableSkills(LivingEntity entity, CustomMonster monster) {
        try {
            if (skillManager != null) {
                for (String skillId : monster.getInheritedSkills()) {
                    skillManager.enableSkill(entity, skillId, monster.getSkillParameters());
                }
            }
        } catch (Exception e) {
            logger.warning("启用技能失败: " + e.getMessage());
        }
    }
    
    /**
     * 播放生成粒子效果
     */
    private void playSpawnEffects(LivingEntity entity, CustomMonster monster) {
        try {
            ParticleEffect spawnEffect = monster.getParticleEffects().get("spawn");
            if (spawnEffect != null) {
                spawnEffect.play(entity.getLocation());
            }
        } catch (Exception e) {
            logger.warning("播放生成粒子效果失败: " + e.getMessage());
        }
    }
    
    /**
     * 处理怪物死亡
     * 
     * @param entity 死亡的实体
     */
    public void handleMonsterDeath(LivingEntity entity) {
        CustomMonster monster = activeMonsters.remove(entity);
        if (monster != null) {
            currentActive--;
            
            try {
                // 播放死亡粒子效果
                ParticleEffect deathEffect = monster.getParticleEffects().get("death");
                if (deathEffect != null) {
                    deathEffect.play(entity.getLocation());
                }
                
                // 处理奖励
                handleRewards(entity, monster);
                
                logger.info("自定义怪物死亡: " + monster.getName() + " (" + monster.getId() + ")");
                
            } catch (Exception e) {
                logger.warning("处理怪物死亡失败: " + e.getMessage());
            }
        }
    }
    
    /**
     * 处理奖励
     */
    private void handleRewards(LivingEntity entity, CustomMonster monster) {
        // 这里需要集成奖励系统
        // 暂时只记录日志
        RewardConfig rewards = monster.getRewards();
        if (rewards != null) {
            double money = rewards.getFinalMoney();
            int experience = rewards.getFinalExperience();
            
            if (money > 0 || experience > 0) {
                logger.info("怪物奖励: 金钱=" + money + ", 经验=" + experience);
            }
        }
    }
    
    /**
     * 检查实体是否为IDZ自定义怪物
     *
     * @param entity 实体
     * @return 是否为IDZ自定义怪物
     */
    public boolean isIDZCustomMonster(LivingEntity entity) {
        return entity.hasMetadata("idzCustomMonster") &&
               entity.hasMetadata("monsterSeries") &&
               "IDZ".equals(entity.getMetadata("monsterSeries").get(0).asString());
    }

    /**
     * 获取IDZ怪物的ID
     *
     * @param entity 实体
     * @return IDZ怪物ID，如果不是IDZ怪物返回null
     */
    public String getIDZMonsterId(LivingEntity entity) {
        if (!isIDZCustomMonster(entity)) {
            return null;
        }
        return entity.getMetadata("idzMonsterId").get(0).asString();
    }

    /**
     * 获取IDZ怪物的名称
     *
     * @param entity 实体
     * @return IDZ怪物名称，如果不是IDZ怪物返回null
     */
    public String getIDZMonsterName(LivingEntity entity) {
        if (!isIDZCustomMonster(entity)) {
            return null;
        }
        return entity.getMetadata("idzMonsterName").get(0).asString();
    }
    
    /**
     * 获取实体对应的自定义怪物配置
     * 
     * @param entity 实体
     * @return 自定义怪物配置，如果不是IDZ怪物返回null
     */
    public CustomMonster getCustomMonster(LivingEntity entity) {
        return activeMonsters.get(entity);
    }
    
    /**
     * 获取活跃的自定义怪物数量
     * 
     * @return 活跃数量
     */
    public int getActiveMonsterCount() {
        return currentActive;
    }
    
    /**
     * 获取总生成数量
     * 
     * @return 总生成数量
     */
    public int getTotalSpawnedCount() {
        return totalSpawned;
    }
    
    /**
     * 清理无效的实体引用
     */
    public void cleanupInvalidEntities() {
        activeMonsters.entrySet().removeIf(entry -> {
            LivingEntity entity = entry.getKey();
            if (entity.isDead() || !entity.isValid()) {
                currentActive--;
                return true;
            }
            return false;
        });
    }
    
    /**
     * 获取统计信息
     * 
     * @return 统计信息字符串
     */
    public String getStatistics() {
        return String.format("IDZ统计: 总生成=%d, 当前活跃=%d", totalSpawned, currentActive);
    }
    
    /**
     * 重置统计信息
     */
    public void resetStatistics() {
        totalSpawned = 0;
        // 不重置currentActive，因为还有活跃的怪物
    }
}
