package org.Ver_zhzh.customZombie.UserMaker;

import org.bukkit.ChatColor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.Player;
import org.bukkit.plugin.Plugin;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.logging.Logger;

/**
 * IDZ命令处理器
 * 处理/czm make相关的命令
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class IDZCommandHandler {
    
    private final Plugin plugin;
    private final Logger logger;
    private final CustomMonsterConfigManager configManager;
    private final IDZEntityManager entityManager;
    private final IDZSystemManager systemManager;
    
    // 命令权限
    private static final String PERMISSION_MAKE = "deathzombie.idz.make";
    private static final String PERMISSION_EDIT = "deathzombie.idz.edit";
    private static final String PERMISSION_DELETE = "deathzombie.idz.delete";
    private static final String PERMISSION_LIST = "deathzombie.idz.list";
    private static final String PERMISSION_GUI = "deathzombie.idz.gui";
    
    /**
     * 构造函数
     *
     * @param plugin 插件实例
     * @param configManager 配置管理器
     * @param entityManager 实体管理器
     * @param systemManager 系统管理器
     */
    public IDZCommandHandler(Plugin plugin, CustomMonsterConfigManager configManager, IDZEntityManager entityManager, IDZSystemManager systemManager) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.configManager = configManager;
        this.entityManager = entityManager;
        this.systemManager = systemManager;
    }
    
    /**
     * 处理IDZ相关命令
     * 
     * @param sender 命令发送者
     * @param args 命令参数
     * @return 是否处理成功
     */
    public boolean handleIDZCommand(CommandSender sender, String[] args) {
        if (args.length < 2) {
            sendIDZHelp(sender);
            return true;
        }
        
        String subCommand = args[1].toLowerCase();
        
        switch (subCommand) {
            case "make":
                return handleMakeCommand(sender, args);
            case "edit":
                return handleEditCommand(sender, args);
            case "delete":
            case "remove":
                return handleDeleteCommand(sender, args);
            case "list":
                return handleListCommand(sender, args);
            case "gui":
                return handleGuiCommand(sender, args);
            case "spawn":
                return handleSpawnCommand(sender, args);
            case "reload":
                return handleReloadCommand(sender, args);
            case "test":
                return handleTestCommand(sender, args);
            case "status":
                return handleStatusCommand(sender, args);
            case "skills":
                return handleSkillsCommand(sender, args);
            case "help":
            default:
                sendIDZHelp(sender);
                return true;
        }
    }
    
    /**
     * 处理make命令
     * /czm make <名称> [实体类型]
     */
    private boolean handleMakeCommand(CommandSender sender, String[] args) {
        if (!sender.hasPermission(PERMISSION_MAKE)) {
            sender.sendMessage(ChatColor.RED + "你没有权限创建自定义怪物！");
            return true;
        }
        
        if (args.length < 3) {
            sender.sendMessage(ChatColor.RED + "用法: /czm make <名称> [实体类型]");
            sender.sendMessage(ChatColor.YELLOW + "示例: /czm make 我的超级僵尸 ZOMBIE");
            return true;
        }
        
        String monsterName = args[2];
        
        // 检查名称是否已存在
        if (configManager.exists(monsterName)) {
            sender.sendMessage(ChatColor.RED + "名称为 '" + monsterName + "' 的自定义怪物已存在！");
            return true;
        }
        
        // 解析实体类型
        EntityType entityType = EntityType.ZOMBIE; // 默认类型
        if (args.length > 3) {
            try {
                entityType = EntityType.valueOf(args[3].toUpperCase());
            } catch (IllegalArgumentException e) {
                sender.sendMessage(ChatColor.RED + "无效的实体类型: " + args[3]);
                sender.sendMessage(ChatColor.YELLOW + "可用类型: " + getAvailableEntityTypes());
                return true;
            }
        }
        
        // 验证实体类型是否支持
        if (!isValidEntityType(entityType)) {
            sender.sendMessage(ChatColor.RED + "不支持的实体类型: " + entityType);
            sender.sendMessage(ChatColor.YELLOW + "支持的类型: " + getSupportedEntityTypes());
            return true;
        }
        
        try {
            // 生成新的IDZ ID
            String idzId = generateNextIDZId();
            
            // 创建自定义怪物
            CustomMonster monster = new CustomMonster(idzId, monsterName, entityType);
            monster.setCreator(sender.getName());
            monster.setDisplayName("§6" + monsterName); // 默认金色名称
            
            // 保存到配置文件
            if (configManager.saveCustomMonster(monster)) {
                sender.sendMessage(ChatColor.GREEN + "成功创建自定义怪物: " + monsterName);
                sender.sendMessage(ChatColor.YELLOW + "ID: " + idzId + ", 类型: " + entityType);
                sender.sendMessage(ChatColor.YELLOW + "使用 /czm edit " + monsterName + " 来编辑属性");
                sender.sendMessage(ChatColor.YELLOW + "使用 /czm make gui 来打开可视化编辑器");
                
                logger.info("玩家 " + sender.getName() + " 创建了自定义怪物: " + monsterName + " (" + idzId + ")");
            } else {
                sender.sendMessage(ChatColor.RED + "创建自定义怪物失败，请检查控制台错误信息");
            }
            
        } catch (Exception e) {
            sender.sendMessage(ChatColor.RED + "创建自定义怪物时发生错误: " + e.getMessage());
            logger.severe("创建自定义怪物失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        return true;
    }
    
    /**
     * 处理edit命令
     * /czm edit <名称>
     */
    private boolean handleEditCommand(CommandSender sender, String[] args) {
        if (!sender.hasPermission(PERMISSION_EDIT)) {
            sender.sendMessage(ChatColor.RED + "你没有权限编辑自定义怪物！");
            return true;
        }
        
        if (args.length < 3) {
            sender.sendMessage(ChatColor.RED + "用法: /czm edit <名称>");
            return true;
        }
        
        String monsterName = args[2];
        CustomMonster monster = configManager.getCustomMonster(monsterName);
        
        if (monster == null) {
            sender.sendMessage(ChatColor.RED + "未找到名称为 '" + monsterName + "' 的自定义怪物！");
            return true;
        }
        
        // 显示怪物信息和编辑选项
        showMonsterInfo(sender, monster);
        
        return true;
    }
    
    /**
     * 处理delete命令
     * /czm delete <名称>
     */
    private boolean handleDeleteCommand(CommandSender sender, String[] args) {
        if (!sender.hasPermission(PERMISSION_DELETE)) {
            sender.sendMessage(ChatColor.RED + "你没有权限删除自定义怪物！");
            return true;
        }
        
        if (args.length < 3) {
            sender.sendMessage(ChatColor.RED + "用法: /czm delete <名称>");
            return true;
        }
        
        String monsterName = args[2];
        
        if (!configManager.exists(monsterName)) {
            sender.sendMessage(ChatColor.RED + "未找到名称为 '" + monsterName + "' 的自定义怪物！");
            return true;
        }
        
        if (configManager.deleteCustomMonster(monsterName)) {
            sender.sendMessage(ChatColor.GREEN + "成功删除自定义怪物: " + monsterName);
            logger.info("玩家 " + sender.getName() + " 删除了自定义怪物: " + monsterName);
        } else {
            sender.sendMessage(ChatColor.RED + "删除自定义怪物失败，请检查控制台错误信息");
        }
        
        return true;
    }
    
    /**
     * 处理list命令
     * /czm list [页码]
     */
    private boolean handleListCommand(CommandSender sender, String[] args) {
        if (!sender.hasPermission(PERMISSION_LIST)) {
            sender.sendMessage(ChatColor.RED + "你没有权限查看自定义怪物列表！");
            return true;
        }
        
        var monsters = configManager.getAllCustomMonsters();
        
        if (monsters.isEmpty()) {
            sender.sendMessage(ChatColor.YELLOW + "当前没有自定义怪物");
            sender.sendMessage(ChatColor.YELLOW + "使用 /czm make <名称> 来创建第一个自定义怪物");
            return true;
        }
        
        // 分页显示
        int page = 1;
        if (args.length > 2) {
            try {
                page = Integer.parseInt(args[2]);
            } catch (NumberFormatException e) {
                page = 1;
            }
        }
        
        int itemsPerPage = 10;
        int totalPages = (int) Math.ceil((double) monsters.size() / itemsPerPage);
        page = Math.max(1, Math.min(page, totalPages));
        
        sender.sendMessage(ChatColor.GOLD + "========== 自定义怪物列表 (第" + page + "/" + totalPages + "页) ==========");
        
        var monsterList = new ArrayList<>(monsters.values());
        int startIndex = (page - 1) * itemsPerPage;
        int endIndex = Math.min(startIndex + itemsPerPage, monsterList.size());
        
        for (int i = startIndex; i < endIndex; i++) {
            CustomMonster monster = monsterList.get(i);
            String status = monster.isEnabled() ? ChatColor.GREEN + "启用" : ChatColor.RED + "禁用";
            sender.sendMessage(String.format("%s%d. %s%s %s(%s) - %s %s生命值: %.1f",
                    ChatColor.YELLOW, i + 1,
                    ChatColor.WHITE, monster.getName(),
                    ChatColor.GRAY, monster.getId(),
                    status,
                    ChatColor.GRAY, monster.getHealth()));
        }
        
        if (totalPages > 1) {
            sender.sendMessage(ChatColor.GRAY + "使用 /czm list " + (page + 1) + " 查看下一页");
        }
        
        return true;
    }
    
    /**
     * 处理gui命令
     * /czm make gui
     */
    private boolean handleGuiCommand(CommandSender sender, String[] args) {
        if (!sender.hasPermission(PERMISSION_GUI)) {
            sender.sendMessage(ChatColor.RED + "你没有权限使用GUI编辑器！");
            return true;
        }
        
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "只有玩家可以使用GUI编辑器！");
            return true;
        }
        
        Player player = (Player) sender;
        
        // 打开WebUI界面
        String webUrl = getWebUIUrl();
        if (webUrl != null) {
            player.sendMessage(ChatColor.GREEN + "请在浏览器中打开以下链接来使用可视化编辑器:");
            player.sendMessage(ChatColor.AQUA + webUrl);
            player.sendMessage(ChatColor.YELLOW + "提示: 你可以点击链接或复制到浏览器地址栏");
        } else {
            player.sendMessage(ChatColor.RED + "WebUI服务未启动，请联系管理员");
        }
        
        return true;
    }
    
    /**
     * 处理spawn命令
     * /czm spawn <名称>
     */
    private boolean handleSpawnCommand(CommandSender sender, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "只有玩家可以生成自定义怪物！");
            return true;
        }
        
        if (args.length < 3) {
            sender.sendMessage(ChatColor.RED + "用法: /czm spawn <名称>");
            return true;
        }
        
        Player player = (Player) sender;
        String monsterName = args[2];
        
        CustomMonster monster = configManager.getCustomMonster(monsterName);
        if (monster == null) {
            player.sendMessage(ChatColor.RED + "未找到名称为 '" + monsterName + "' 的自定义怪物！");
            return true;
        }
        
        if (!monster.isEnabled()) {
            player.sendMessage(ChatColor.RED + "自定义怪物 '" + monsterName + "' 已被禁用！");
            return true;
        }
        
        try {
            // 在玩家位置生成怪物
            if (entityManager.spawnCustomMonster(player.getLocation(), monster)) {
                player.sendMessage(ChatColor.GREEN + "成功生成自定义怪物: " + monsterName);
            } else {
                player.sendMessage(ChatColor.RED + "生成自定义怪物失败");
            }
        } catch (Exception e) {
            player.sendMessage(ChatColor.RED + "生成怪物时发生错误: " + e.getMessage());
            logger.warning("生成自定义怪物失败: " + e.getMessage());
        }
        
        return true;
    }

    /**
     * 处理test命令
     * /czm test [quick|full]
     */
    private boolean handleTestCommand(CommandSender sender, String[] args) {
        if (!sender.hasPermission("deathzombie.admin")) {
            sender.sendMessage(ChatColor.RED + "你没有权限运行系统测试！");
            return true;
        }

        String testType = args.length > 2 ? args[2].toLowerCase() : "quick";

        sender.sendMessage(ChatColor.YELLOW + "正在运行IDZ系统测试...");

        try {
            boolean testResult;

            if ("full".equals(testType) && sender instanceof Player) {
                // 完整测试需要玩家位置
                Player player = (Player) sender;
                IDZSystemManager systemManager = getSystemManager();
                testResult = systemManager.runSystemTest(player.getLocation());
            } else {
                // 快速测试
                IDZSystemManager systemManager = getSystemManager();
                testResult = systemManager.runSystemTest(null);
            }

            if (testResult) {
                sender.sendMessage(ChatColor.GREEN + "IDZ系统测试通过！");
            } else {
                sender.sendMessage(ChatColor.RED + "IDZ系统测试失败，请检查控制台日志");
            }

        } catch (Exception e) {
            sender.sendMessage(ChatColor.RED + "运行测试时发生错误: " + e.getMessage());
            logger.warning("运行IDZ系统测试失败: " + e.getMessage());
        }

        return true;
    }

    /**
     * 处理status命令
     * /czm status
     */
    private boolean handleStatusCommand(CommandSender sender, String[] args) {
        if (!sender.hasPermission("deathzombie.admin")) {
            sender.sendMessage(ChatColor.RED + "你没有权限查看系统状态！");
            return true;
        }

        try {
            IDZSystemManager systemManager = getSystemManager();

            sender.sendMessage(ChatColor.GOLD + "========== IDZ系统状态 ==========");
            sender.sendMessage(ChatColor.YELLOW + "版本: " + ChatColor.WHITE + systemManager.getVersionInfo());

            String[] statusLines = systemManager.getSystemStatus().split("\n");
            for (String line : statusLines) {
                sender.sendMessage(ChatColor.YELLOW + line);
            }

            // 显示测试报告
            sender.sendMessage(ChatColor.GOLD + "========== 测试报告 ==========");
            String[] reportLines = systemManager.getSystemTestReport().split("\n");
            for (String line : reportLines) {
                sender.sendMessage(ChatColor.GRAY + line);
            }

        } catch (Exception e) {
            sender.sendMessage(ChatColor.RED + "获取系统状态失败: " + e.getMessage());
        }

        return true;
    }

    /**
     * 处理skills命令
     * /czm skills [summary|detail|validate]
     */
    private boolean handleSkillsCommand(CommandSender sender, String[] args) {
        if (!sender.hasPermission("deathzombie.admin")) {
            sender.sendMessage(ChatColor.RED + "你没有权限查看技能统计！");
            return true;
        }

        String subCommand = args.length > 2 ? args[2].toLowerCase() : "summary";

        try {
            SkillTemplateManager skillManager = systemManager.getSkillManager();
            if (skillManager == null) {
                sender.sendMessage(ChatColor.RED + "技能管理器未初始化");
                return true;
            }

            SkillStatistics skillStats = new SkillStatistics(skillManager, logger);

            switch (subCommand) {
                case "detail":
                    sender.sendMessage(ChatColor.GOLD + "正在生成详细技能统计报告...");
                    String detailReport = skillStats.generateDetailedReport();
                    String[] detailLines = detailReport.split("\n");
                    for (String line : detailLines) {
                        sender.sendMessage(ChatColor.YELLOW + line);
                    }
                    break;

                case "validate":
                    sender.sendMessage(ChatColor.YELLOW + "正在验证技能数量...");
                    boolean isValid = skillStats.validateSkillCounts();
                    if (isValid) {
                        sender.sendMessage(ChatColor.GREEN + "技能数量验证通过 ✓");
                        sender.sendMessage(ChatColor.GREEN + skillStats.generateSummaryReport());
                    } else {
                        sender.sendMessage(ChatColor.RED + "技能数量验证失败 ✗");
                        sender.sendMessage(ChatColor.YELLOW + skillStats.generateSummaryReport());

                        // 显示缺失技能信息
                        String missingReport = skillStats.findMissingSkills();
                        String[] missingLines = missingReport.split("\n");
                        for (String line : missingLines) {
                            sender.sendMessage(ChatColor.GRAY + line);
                        }
                    }
                    break;

                case "summary":
                default:
                    sender.sendMessage(ChatColor.GOLD + "========== IDZ技能统计 ==========");
                    sender.sendMessage(ChatColor.YELLOW + skillStats.generateSummaryReport());

                    // 显示验证状态
                    if (skillStats.validateSkillCounts()) {
                        sender.sendMessage(ChatColor.GREEN + "技能数量验证: 通过 ✓");
                    } else {
                        sender.sendMessage(ChatColor.RED + "技能数量验证: 失败 ✗");
                        sender.sendMessage(ChatColor.GRAY + "使用 /czm skills validate 查看详细信息");
                    }
                    break;
            }

        } catch (Exception e) {
            sender.sendMessage(ChatColor.RED + "获取技能统计失败: " + e.getMessage());
            logger.warning("获取技能统计失败: " + e.getMessage());
        }

        return true;
    }

    /**
     * 获取系统管理器
     */
    private IDZSystemManager getSystemManager() {
        return systemManager;
    }
    
    /**
     * 处理reload命令
     * /czm reload
     */
    private boolean handleReloadCommand(CommandSender sender, String[] args) {
        if (!sender.hasPermission("deathzombie.admin")) {
            sender.sendMessage(ChatColor.RED + "你没有权限重新加载配置！");
            return true;
        }
        
        try {
            configManager.reloadConfig();
            sender.sendMessage(ChatColor.GREEN + "IDZ配置文件重新加载完成");
        } catch (Exception e) {
            sender.sendMessage(ChatColor.RED + "重新加载配置失败: " + e.getMessage());
        }
        
        return true;
    }
    
    /**
     * 显示怪物信息
     */
    private void showMonsterInfo(CommandSender sender, CustomMonster monster) {
        sender.sendMessage(ChatColor.GOLD + "========== " + monster.getName() + " ==========");
        sender.sendMessage(ChatColor.YELLOW + "ID: " + ChatColor.WHITE + monster.getId());
        sender.sendMessage(ChatColor.YELLOW + "显示名称: " + monster.getDisplayName());
        sender.sendMessage(ChatColor.YELLOW + "实体类型: " + ChatColor.WHITE + monster.getEntityType());
        sender.sendMessage(ChatColor.YELLOW + "生命值: " + ChatColor.WHITE + monster.getHealth());
        sender.sendMessage(ChatColor.YELLOW + "攻击力: " + ChatColor.WHITE + monster.getDamage());
        sender.sendMessage(ChatColor.YELLOW + "移动速度: " + ChatColor.WHITE + monster.getSpeed());
        sender.sendMessage(ChatColor.YELLOW + "状态: " + (monster.isEnabled() ? ChatColor.GREEN + "启用" : ChatColor.RED + "禁用"));
        sender.sendMessage(ChatColor.YELLOW + "创建者: " + ChatColor.WHITE + monster.getCreator());
        
        if (!monster.getInheritedSkills().isEmpty()) {
            sender.sendMessage(ChatColor.YELLOW + "继承技能: " + ChatColor.WHITE + String.join(", ", monster.getInheritedSkills()));
        }
        
        sender.sendMessage(ChatColor.GRAY + "使用 /czm make gui 打开可视化编辑器进行详细配置");
    }
    
    /**
     * 发送IDZ帮助信息
     */
    private void sendIDZHelp(CommandSender sender) {
        sender.sendMessage(ChatColor.GOLD + "========== IDZ独立怪物系列 ==========");
        sender.sendMessage(ChatColor.AQUA + "IDZ (Independent DeathZombie) - 完全自定义的独立怪物系列");
        sender.sendMessage(ChatColor.GRAY + "特点: 继承ID+IDC技能，支持粒子效果，独立元数据标记");
        sender.sendMessage("");
        sender.sendMessage(ChatColor.YELLOW + "/czm make <名称> [实体类型] " + ChatColor.GRAY + "- 创建新的IDZ怪物");
        sender.sendMessage(ChatColor.YELLOW + "/czm make gui " + ChatColor.GRAY + "- 打开可视化编辑器");
        sender.sendMessage(ChatColor.YELLOW + "/czm edit <名称> " + ChatColor.GRAY + "- 查看怪物信息");
        sender.sendMessage(ChatColor.YELLOW + "/czm list [页码] " + ChatColor.GRAY + "- 查看IDZ怪物列表");
        sender.sendMessage(ChatColor.YELLOW + "/czm spawn <名称> " + ChatColor.GRAY + "- 生成IDZ怪物");
        sender.sendMessage(ChatColor.YELLOW + "/czm delete <名称> " + ChatColor.GRAY + "- 删除IDZ怪物");
        sender.sendMessage(ChatColor.YELLOW + "/czm reload " + ChatColor.GRAY + "- 重新加载配置");
        sender.sendMessage(ChatColor.YELLOW + "/czm test [quick|full] " + ChatColor.GRAY + "- 运行系统测试");
        sender.sendMessage(ChatColor.YELLOW + "/czm status " + ChatColor.GRAY + "- 查看系统状态");
        sender.sendMessage(ChatColor.YELLOW + "/czm skills [summary|detail|validate] " + ChatColor.GRAY + "- 查看技能统计");
        sender.sendMessage("");
        sender.sendMessage(ChatColor.GRAY + "支持的实体类型: " + getSupportedEntityTypes());
        sender.sendMessage(ChatColor.GRAY + "可继承技能: ID系列(35个) + IDC系列(50个) = 85个技能模板");
        sender.sendMessage(ChatColor.GRAY + "技能来源: ID5-ID25, IDC1-IDC22");
    }
    
    /**
     * 生成下一个IDZ ID
     */
    private String generateNextIDZId() {
        var monsters = configManager.getAllCustomMonsters();
        int maxId = 0;
        
        for (CustomMonster monster : monsters.values()) {
            String id = monster.getId();
            if (id != null && id.startsWith("idz")) {
                try {
                    int num = Integer.parseInt(id.substring(3));
                    maxId = Math.max(maxId, num);
                } catch (NumberFormatException e) {
                    // 忽略无效的ID格式
                }
            }
        }
        
        return "idz" + (maxId + 1);
    }
    
    /**
     * 检查实体类型是否有效
     */
    private boolean isValidEntityType(EntityType entityType) {
        // 只允许生物类型的实体
        return entityType.isAlive() && !entityType.equals(EntityType.PLAYER);
    }
    
    /**
     * 获取支持的实体类型
     */
    private String getSupportedEntityTypes() {
        List<String> types = Arrays.asList(
                "ZOMBIE", "SKELETON", "SPIDER", "CREEPER", "ENDERMAN",
                "BLAZE", "WITCH", "PILLAGER", "EVOKER", "RAVAGER",
                "ZOMBIFIED_PIGLIN", "PIGLIN", "HOGLIN", "ZOGLIN",
                "WITHER_SKELETON", "STRAY", "HUSK", "DROWNED",
                "PHANTOM", "SHULKER", "ENDERMITE", "SILVERFISH",
                "MAGMA_CUBE", "SLIME", "GHAST", "WITHER", "ENDER_DRAGON"
        );
        return String.join(", ", types);
    }
    
    /**
     * 获取所有可用的实体类型
     */
    private String getAvailableEntityTypes() {
        return getSupportedEntityTypes();
    }
    
    /**
     * 获取WebUI URL
     */
    private String getWebUIUrl() {
        // 这里需要从主插件获取WebUI的URL
        // 暂时返回默认值，后续需要集成
        return "http://localhost:8080/idz-editor";
    }
    
    /**
     * 获取命令补全建议
     * 
     * @param args 命令参数
     * @return 补全建议列表
     */
    public List<String> getTabCompletions(String[] args) {
        List<String> completions = new ArrayList<>();
        
        if (args.length == 2) {
            // 子命令补全
            completions.addAll(Arrays.asList("make", "edit", "delete", "list", "spawn", "gui", "reload", "help"));
        } else if (args.length == 3) {
            String subCommand = args[1].toLowerCase();
            switch (subCommand) {
                case "edit":
                case "delete":
                case "spawn":
                    // 怪物名称补全
                    completions.addAll(configManager.getAllCustomMonsters().keySet());
                    break;
                case "make":
                    if (args[2].equals("gui")) {
                        // 不需要更多参数
                    } else {
                        // 提示输入名称
                        completions.add("<怪物名称>");
                    }
                    break;
            }
        } else if (args.length == 4 && args[1].equalsIgnoreCase("make")) {
            // 实体类型补全
            completions.addAll(Arrays.asList("ZOMBIE", "SKELETON", "SPIDER", "CREEPER", "BLAZE"));
        }
        
        return completions;
    }
}
