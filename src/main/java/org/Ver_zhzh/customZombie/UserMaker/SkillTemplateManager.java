package org.Ver_zhzh.customZombie.UserMaker;

import org.bukkit.entity.LivingEntity;
import org.bukkit.plugin.Plugin;

import java.util.*;
import java.util.logging.Logger;

/**
 * 技能模板管理器
 * 负责管理和应用从ID/IDC系列继承的技能模板
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class SkillTemplateManager {
    
    private final Plugin plugin;
    private final Logger logger;
    
    // 技能模板注册表
    private final Map<String, SkillTemplate> skillTemplates;
    
    // 活跃技能实例
    private final Map<LivingEntity, Set<String>> activeSkills;
    
    /**
     * 技能模板内部类
     */
    public static class SkillTemplate {
        private final String id;
        private final String name;
        private final String description;
        private final String sourceEntity; // 来源实体 (id5, idc3等)
        private final SkillType type;
        private final Map<String, Object> defaultParameters;
        private final SkillExecutor executor;
        
        public enum SkillType {
            PASSIVE,        // 被动技能
            ACTIVE,         // 主动技能
            TRIGGER,        // 触发技能
            AURA,           // 光环技能
            PERIODIC        // 周期技能
        }
        
        public SkillTemplate(String id, String name, String description, String sourceEntity, 
                           SkillType type, Map<String, Object> defaultParameters, SkillExecutor executor) {
            this.id = id;
            this.name = name;
            this.description = description;
            this.sourceEntity = sourceEntity;
            this.type = type;
            this.defaultParameters = defaultParameters != null ? defaultParameters : new HashMap<>();
            this.executor = executor;
        }
        
        // Getter方法
        public String getId() { return id; }
        public String getName() { return name; }
        public String getDescription() { return description; }
        public String getSourceEntity() { return sourceEntity; }
        public SkillType getType() { return type; }
        public Map<String, Object> getDefaultParameters() { return defaultParameters; }
        public SkillExecutor getExecutor() { return executor; }
    }
    
    /**
     * 技能执行器接口
     */
    @FunctionalInterface
    public interface SkillExecutor {
        void execute(LivingEntity entity, Map<String, Object> parameters);
    }
    
    /**
     * 构造函数
     * 
     * @param plugin 插件实例
     */
    public SkillTemplateManager(Plugin plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.skillTemplates = new HashMap<>();
        this.activeSkills = new HashMap<>();
        
        // 初始化技能模板
        initializeSkillTemplates();
    }
    
    /**
     * 初始化技能模板
     */
    private void initializeSkillTemplates() {
        logger.info("开始初始化技能模板...");
        
        // 注册ID系列技能模板
        registerIDSkillTemplates();
        
        // 注册IDC系列技能模板
        registerIDCSkillTemplates();
        
        logger.info("技能模板初始化完成，共注册 " + skillTemplates.size() + " 个技能模板");
    }
    
    /**
     * 注册ID系列技能模板（35个技能）
     */
    private void registerIDSkillTemplates() {
        // ID5 - 剧毒攻击
        registerSkillTemplate(new SkillTemplate(
            "id5_poison_attack", "剧毒攻击", "攻击时使玩家获得剧毒buff", "id5",
            SkillTemplate.SkillType.TRIGGER, createParams("poison_level", 1, "poison_duration", 3000),
            this::executePoisonAttack
        ));

        // ID6 - 死亡召唤
        registerSkillTemplate(new SkillTemplate(
            "id6_death_summon", "死亡召唤", "死亡时召唤X个僵尸", "id6",
            SkillTemplate.SkillType.TRIGGER, createParams("summon_count", 2, "summon_type", "ZOMBIE"),
            this::executeDeathSummon
        ));

        // ID7 - 箭矢攻击
        registerSkillTemplate(new SkillTemplate(
            "id7_arrow_attack", "箭矢攻击", "每X秒发射X箭矢攻击", "id7",
            SkillTemplate.SkillType.PERIODIC, createParams("arrow_interval", 5000, "arrow_count", 3),
            this::executeArrowAttack
        ));

        // ID10 - 法师召唤
        registerSkillTemplate(new SkillTemplate(
            "id10_mage_summon", "法师召唤", "每X秒召唤X个僵尸", "id10",
            SkillTemplate.SkillType.PERIODIC, createParams("summon_interval", 10000, "summon_count", 2),
            this::executeMageSummon
        ));

        // ID11 - 自爆攻击
        registerSkillTemplate(new SkillTemplate(
            "id11_explode_attack", "自爆攻击", "在玩家身边时自动进入爆炸倒计时", "id11",
            SkillTemplate.SkillType.TRIGGER, createParams("explode_delay", 3000, "explode_damage", 20.0),
            this::executeExplodeAttack
        ));

        // ID12 - 毒箭攻击
        registerSkillTemplate(new SkillTemplate(
            "id12_poison_arrow", "毒箭攻击", "每X秒发射X带有XXXbuff的箭矢攻击", "id12",
            SkillTemplate.SkillType.PERIODIC, createParams("arrow_interval", 8000, "poison_level", 2),
            this::executePoisonArrow
        ));

        // ID13 - 电击攻击
        registerSkillTemplate(new SkillTemplate(
            "id13_lightning_attack", "电击攻击", "每X秒对玩家造成电击", "id13",
            SkillTemplate.SkillType.PERIODIC, createParams("lightning_interval", 6000, "lightning_damage", 8.0),
            this::executeLightningAttack
        ));

        // ID14 - 冰冻攻击
        registerSkillTemplate(new SkillTemplate(
            "id14_freeze_attack", "冰冻攻击", "每X秒对玩家造成冰冻", "id14",
            SkillTemplate.SkillType.PERIODIC, createParams("freeze_interval", 8000, "freeze_duration", 3000),
            this::executeFreezeAttack
        ));

        // ID15 - 传送攻击
        registerSkillTemplate(new SkillTemplate(
            "id15_teleport_attack", "传送攻击", "每X秒传送到玩家身边", "id15",
            SkillTemplate.SkillType.PERIODIC, createParams("teleport_interval", 10000, "teleport_range", 15.0),
            this::executeTeleportAttack
        ));

        // ID17 - 雷霆攻击
        registerSkillTemplate(new SkillTemplate(
            "id17_thunder_attack", "雷霆攻击", "每X秒对玩家造成X次闪电攻击", "id17",
            SkillTemplate.SkillType.PERIODIC, createParams("thunder_interval", 12000, "thunder_count", 3),
            this::executeThunderAttack
        ));

        // ID18 - 变异科学家（4个技能）
        registerSkillTemplate(new SkillTemplate(
            "id18_damage_summon", "受伤召唤", "每受到50点伤害召唤X僵尸一次", "id18",
            SkillTemplate.SkillType.TRIGGER, createParams("damage_threshold", 50, "summon_count", 2),
            this::executeDamageSummon
        ));
        registerSkillTemplate(new SkillTemplate(
            "id18_area_debuff", "范围减益", "玩家在10*10范围内获得反胃、减速效果", "id18",
            SkillTemplate.SkillType.AURA, createParams("range", 10, "nausea_level", 1, "slowness_level", 1),
            this::executeAreaDebuff
        ));
        registerSkillTemplate(new SkillTemplate(
            "id18_global_damage", "全局伤害", "每20秒对所有玩家直接造成10点伤害", "id18",
            SkillTemplate.SkillType.PERIODIC, createParams("damage_interval", 20000, "damage_amount", 10.0),
            this::executeGlobalDamage
        ));
        registerSkillTemplate(new SkillTemplate(
            "id18_death_summon", "死亡召唤", "死亡后召唤X个僵尸", "id18",
            SkillTemplate.SkillType.TRIGGER, createParams("summon_count", 3, "summon_type", "ZOMBIE"),
            this::executeDeathSummon
        ));

        // ID19 - 变异法师（3个技能）
        registerSkillTemplate(new SkillTemplate(
            "id19_area_lightning", "范围闪电", "每X秒使在6*6范围的玩家被闪电击中", "id19",
            SkillTemplate.SkillType.PERIODIC, createParams("lightning_interval", 8000, "range", 6),
            this::executeAreaLightning
        ));
        registerSkillTemplate(new SkillTemplate(
            "id19_area_buff", "范围增益", "玩家在10*10范围内获得buff效果", "id19",
            SkillTemplate.SkillType.AURA, createParams("range", 10, "buff_type", "STRENGTH"),
            this::executeAreaBuff
        ));
        registerSkillTemplate(new SkillTemplate(
            "id19_detect_summon", "检测召唤", "每X秒检测附近5*5范围是否有玩家，如果有就生成X个僵尸", "id19",
            SkillTemplate.SkillType.PERIODIC, createParams("detect_interval", 15000, "detect_range", 5),
            this::executeDetectSummon
        ));

        // ID20 - 气球僵尸（2个技能）
        registerSkillTemplate(new SkillTemplate(
            "id20_flight", "飞行效果", "拥有飞行效果，缓慢上升", "id20",
            SkillTemplate.SkillType.PASSIVE, createParams("flight_speed", 0.1, "max_height", 10),
            this::executeFlight
        ));
        registerSkillTemplate(new SkillTemplate(
            "id20_death_balloons", "死亡气球", "死亡后释放五彩气球粒子，随机弹跳", "id20",
            SkillTemplate.SkillType.TRIGGER, createParams("balloon_count", 10, "bounce_duration", 5000),
            this::executeDeathBalloons
        ));

        // ID21 - 迷雾僵尸（2个技能）
        registerSkillTemplate(new SkillTemplate(
            "id21_fog_generation", "迷雾生成", "定期在周围生成迷雾，降低附近玩家的视野和移动速度", "id21",
            SkillTemplate.SkillType.PERIODIC, createParams("fog_interval", 10000, "fog_duration", 8000),
            this::executeFogGeneration
        ));
        registerSkillTemplate(new SkillTemplate(
            "id21_death_fog", "死亡雾气", "死亡后在周围生成大量雾气粒子", "id21",
            SkillTemplate.SkillType.TRIGGER, createParams("fog_particle_count", 50, "fog_spread", 5.0),
            this::executeDeathFog
        ));

        // ID22 - 变异雷霆僵尸（5个技能）
        registerSkillTemplate(new SkillTemplate(
            "id22_global_lightning", "全局雷电", "每X秒释放一次雷电，对所有玩家造成伤害", "id22",
            SkillTemplate.SkillType.PERIODIC, createParams("lightning_interval", 15000, "lightning_damage", 12.0),
            this::executeGlobalLightning
        ));
        registerSkillTemplate(new SkillTemplate(
            "id22_teleport_lightning", "瞬移雷击", "瞬移到玩家身边并释放雷电", "id22",
            SkillTemplate.SkillType.PERIODIC, createParams("teleport_interval", 20000, "lightning_damage", 15.0),
            this::executeTeleportLightning
        ));
        registerSkillTemplate(new SkillTemplate(
            "id22_summon_guards", "召唤护卫", "召唤雷电护卫", "id22",
            SkillTemplate.SkillType.PERIODIC, createParams("summon_interval", 25000, "guard_count", 2),
            this::executeSummonGuards
        ));
        registerSkillTemplate(new SkillTemplate(
            "id22_time_control", "时间控制", "控制时间流速", "id22",
            SkillTemplate.SkillType.ACTIVE, createParams("time_factor", 0.5, "duration", 10000),
            this::executeTimeControl
        ));
        registerSkillTemplate(new SkillTemplate(
            "id22_freeze_all", "全体冻结", "冻结所有玩家", "id22",
            SkillTemplate.SkillType.ACTIVE, createParams("freeze_duration", 5000, "freeze_damage", 8.0),
            this::executeFreezeAll
        ));

        // ID23 - 变异守护者（4个技能）
        registerSkillTemplate(new SkillTemplate(
            "id23_attack_buff", "攻击增益", "攻击时获得力量buff", "id23",
            SkillTemplate.SkillType.TRIGGER, createParams("buff_duration", 10000, "strength_level", 2),
            this::executeAttackBuff
        ));
        registerSkillTemplate(new SkillTemplate(
            "id23_summon_minions", "召唤小兵", "召唤守护小兵", "id23",
            SkillTemplate.SkillType.PERIODIC, createParams("summon_interval", 18000, "minion_count", 3),
            this::executeSummonMinions
        ));
        registerSkillTemplate(new SkillTemplate(
            "id23_aura_buff", "光环增益", "为周围实体提供增益光环", "id23",
            SkillTemplate.SkillType.AURA, createParams("aura_range", 8, "buff_type", "REGENERATION"),
            this::executeAuraBuff
        ));
        registerSkillTemplate(new SkillTemplate(
            "id23_explosion", "守护爆炸", "死亡时产生强力爆炸", "id23",
            SkillTemplate.SkillType.TRIGGER, createParams("explosion_power", 4.0, "explosion_damage", 25.0),
            this::executeExplosion
        ));

        // ID24 - 变异传送者（3个技能）
        registerSkillTemplate(new SkillTemplate(
            "id24_teleport_attack", "瞬移攻击", "瞬移到玩家身边进行攻击", "id24",
            SkillTemplate.SkillType.PERIODIC, createParams("teleport_interval", 12000, "attack_damage", 18.0),
            this::executeTeleportAttack
        ));
        registerSkillTemplate(new SkillTemplate(
            "id24_continuous_summon", "持续召唤", "持续召唤传送门怪物", "id24",
            SkillTemplate.SkillType.PERIODIC, createParams("summon_interval", 8000, "portal_count", 2),
            this::executeContinuousSummon
        ));
        registerSkillTemplate(new SkillTemplate(
            "id24_global_debuff", "全局减益", "对所有玩家施加减益效果", "id24",
            SkillTemplate.SkillType.PERIODIC, createParams("debuff_interval", 20000, "debuff_duration", 15000),
            this::executeGlobalDebuff
        ));

        // ID25 - 终极变异体（8个技能）
        registerSkillTemplate(new SkillTemplate(
            "id25_random_summon", "随机召唤", "随机召唤各种怪物", "id25",
            SkillTemplate.SkillType.PERIODIC, createParams("summon_interval", 10000, "summon_types", 5),
            this::executeRandomSummon
        ));
        registerSkillTemplate(new SkillTemplate(
            "id25_damage_summon", "受伤召唤", "受伤时召唤援兵", "id25",
            SkillTemplate.SkillType.TRIGGER, createParams("damage_threshold", 30, "summon_count", 2),
            this::executeDamageSummon
        ));
        registerSkillTemplate(new SkillTemplate(
            "id25_ranged_attack", "远程攻击", "发射多种远程攻击", "id25",
            SkillTemplate.SkillType.PERIODIC, createParams("attack_interval", 6000, "projectile_count", 5),
            this::executeRangedAttack
        ));
        registerSkillTemplate(new SkillTemplate(
            "id25_electric_attack", "电击攻击", "释放强力电击", "id25",
            SkillTemplate.SkillType.PERIODIC, createParams("electric_interval", 8000, "electric_damage", 15.0),
            this::executeElectricAttack
        ));
        registerSkillTemplate(new SkillTemplate(
            "id25_attack_buff", "攻击增益", "攻击时获得多重buff", "id25",
            SkillTemplate.SkillType.TRIGGER, createParams("buff_duration", 12000, "buff_count", 3),
            this::executeAttackBuff
        ));
        registerSkillTemplate(new SkillTemplate(
            "id25_freeze_attack", "冰冻攻击", "冰冻周围所有玩家", "id25",
            SkillTemplate.SkillType.PERIODIC, createParams("freeze_interval", 15000, "freeze_range", 12),
            this::executeFreezeAttack
        ));
        registerSkillTemplate(new SkillTemplate(
            "id25_area_electric", "范围电流", "释放大范围电流攻击", "id25",
            SkillTemplate.SkillType.PERIODIC, createParams("electric_interval", 12000, "electric_range", 15),
            this::executeAreaElectric
        ));
        registerSkillTemplate(new SkillTemplate(
            "id25_special_summon", "特殊召唤", "召唤特殊强化怪物", "id25",
            SkillTemplate.SkillType.PERIODIC, createParams("summon_interval", 30000, "special_count", 1),
            this::executeSpecialSummon
        ));

        logger.info("已注册 ID 系列技能模板（共35个技能）");
    }
    
    /**
     * 注册IDC系列技能模板（50个技能）
     */
    private void registerIDCSkillTemplates() {
        // IDC1 - 变异僵尸01（1个技能）
        registerSkillTemplate(new SkillTemplate(
            "idc1_enhanced_poison", "增强剧毒攻击", "每次攻击对玩家施加剧毒效果并造成额外伤害", "idc1",
            SkillTemplate.SkillType.TRIGGER, createParams("poison_level", 2, "poison_duration", 5000, "poison_damage", 3.0),
            this::executeEnhancedPoison
        ));

        // IDC2 - 变异僵尸02（无技能）

        // IDC3 - 变异烈焰人（1个技能）
        registerSkillTemplate(new SkillTemplate(
            "idc3_flame_attack", "烈焰攻击", "每X秒向玩家发射烈焰粒子和烈焰弹", "idc3",
            SkillTemplate.SkillType.PERIODIC, createParams("flame_interval", 8000, "flame_damage", 6.0, "fireball_damage", 10.0),
            this::executeFlameAttack
        ));

        // IDC4 - 变异爬行者（1个技能）
        registerSkillTemplate(new SkillTemplate(
            "idc4_charged_lightning", "充能闪电攻击", "每X秒对最近的玩家造成闪电伤害", "idc4",
            SkillTemplate.SkillType.PERIODIC, createParams("lightning_interval", 10000, "lightning_damage", 15.0),
            this::executeChargedLightning
        ));

        // IDC5 - 变异末影螨（1个技能）
        registerSkillTemplate(new SkillTemplate(
            "idc5_area_electric", "范围电流", "每X秒对10*10范围内释放电流", "idc5",
            SkillTemplate.SkillType.PERIODIC, createParams("electric_interval", 6000, "electric_range", 10),
            this::executeAreaElectric
        ));

        // IDC6 - 变异蜘蛛（2个技能）
        registerSkillTemplate(new SkillTemplate(
            "idc6_web_trap", "蜘蛛网陷阱", "在玩家脚下创造蜘蛛网", "idc6",
            SkillTemplate.SkillType.PERIODIC, createParams("web_duration", 5000, "web_interval", 12000),
            this::executeWebTrap
        ));
        registerSkillTemplate(new SkillTemplate(
            "idc6_poison_release", "毒液释放", "释放毒液使玩家获得剧毒", "idc6",
            SkillTemplate.SkillType.TRIGGER, createParams("poison_level", 1, "poison_duration", 3000),
            this::executePoisonRelease
        ));

        // IDC7 - 灾厄卫道士（3个技能）
        registerSkillTemplate(new SkillTemplate(
            "idc7_summon_vindicators", "召唤卫道士", "每X秒召唤两个卫道士", "idc7",
            SkillTemplate.SkillType.PERIODIC, createParams("summon_interval", 15000, "summon_count", 2),
            this::executeSummonVindicators
        ));
        registerSkillTemplate(new SkillTemplate(
            "idc7_particle_ball", "粒子球攻击", "发射粒子球对玩家造成伤害", "idc7",
            SkillTemplate.SkillType.PERIODIC, createParams("ball_interval", 8000, "ball_damage", 6.0),
            this::executeParticleBall
        ));
        registerSkillTemplate(new SkillTemplate(
            "idc7_aura_damage", "光环伤害", "脚底光环，在光环内每X秒造成伤害", "idc7",
            SkillTemplate.SkillType.AURA, createParams("aura_range", 3, "aura_damage", 4.0, "aura_interval", 2000),
            this::executeAuraDamage
        ));

        // IDC8 - 灾厄幻魔者（4个技能）
        registerSkillTemplate(new SkillTemplate(
            "idc8_summon_vindicators", "召唤卫道士", "召唤卫道士援兵", "idc8",
            SkillTemplate.SkillType.PERIODIC, createParams("summon_interval", 18000, "summon_count", 2),
            this::executeSummonVindicators
        ));
        registerSkillTemplate(new SkillTemplate(
            "idc8_dna_spiral", "DNA螺旋魔法", "粒子DNA螺旋魔法攻击", "idc8",
            SkillTemplate.SkillType.PERIODIC, createParams("spiral_interval", 10000, "spiral_damage", 8.0),
            this::executeDNASpiral
        ));
        registerSkillTemplate(new SkillTemplate(
            "idc8_fang_attack", "尖牙攻击", "圈式内收缩尖牙攻击", "idc8",
            SkillTemplate.SkillType.PERIODIC, createParams("fang_interval", 12000, "fang_damage", 10.0),
            this::executeFangAttack
        ));
        registerSkillTemplate(new SkillTemplate(
            "idc8_aura_damage", "光环伤害", "脚底粒子圆圈光环伤害", "idc8",
            SkillTemplate.SkillType.AURA, createParams("aura_range", 4, "aura_damage", 5.0, "aura_interval", 2000),
            this::executeAuraDamage
        ));

        // IDC9 - 灾厄劫掠兽（1个技能）
        registerSkillTemplate(new SkillTemplate(
            "idc9_crit_aura", "暴击光环", "脚底暴击圆圈光环伤害", "idc9",
            SkillTemplate.SkillType.AURA, createParams("aura_range", 3, "crit_damage", 12.0, "aura_interval", 2000),
            this::executeCritAura
        ));

        // IDC10 - 变异僵尸马（3个技能）
        registerSkillTemplate(new SkillTemplate(
            "idc10_charge_damage", "撞击伤害", "冲撞造成额外伤害", "idc10",
            SkillTemplate.SkillType.TRIGGER, createParams("charge_damage", 15.0, "charge_knockback", 2.0),
            this::executeChargeDamage
        ));
        registerSkillTemplate(new SkillTemplate(
            "idc10_summon_armed", "召唤武装僵尸", "每X秒召唤武装僵尸", "idc10",
            SkillTemplate.SkillType.PERIODIC, createParams("summon_interval", 20000, "armed_count", 2),
            this::executeSummonArmed
        ));
        registerSkillTemplate(new SkillTemplate(
            "idc10_flight", "飞行能力", "拥有飞行能力", "idc10",
            SkillTemplate.SkillType.PASSIVE, createParams("flight_speed", 0.2, "max_height", 15),
            this::executeFlight
        ));

        // IDC11 - 变异岩浆怪（1个技能）
        registerSkillTemplate(new SkillTemplate(
            "idc11_fire_aura", "火焰光环", "脚底火焰粒子环光环伤害", "idc11",
            SkillTemplate.SkillType.AURA, createParams("aura_range", 4, "fire_damage", 6.0, "fire_effect", true),
            this::executeFireAura
        ));

        // IDC12 - 变异尸壳（2个技能）
        registerSkillTemplate(new SkillTemplate(
            "idc12_summon_zombies", "召唤僵尸", "召唤普通僵尸援兵", "idc12",
            SkillTemplate.SkillType.PERIODIC, createParams("summon_interval", 15000, "zombie_count", 3),
            this::executeSummonZombies
        ));
        registerSkillTemplate(new SkillTemplate(
            "idc12_inventory_chaos", "物品栏混乱", "打乱玩家物品栏", "idc12",
            SkillTemplate.SkillType.PERIODIC, createParams("chaos_interval", 25000, "chaos_duration", 5000),
            this::executeInventoryChaos
        ));

        // IDC13 - 变异骷髅（2个技能）
        registerSkillTemplate(new SkillTemplate(
            "idc13_triple_shot", "三连射箭", "发射三支箭矢", "idc13",
            SkillTemplate.SkillType.PERIODIC, createParams("shot_interval", 6000, "arrow_damage", 8.0),
            this::executeTripleShot
        ));
        registerSkillTemplate(new SkillTemplate(
            "idc13_frost_aura", "冰霜光环", "冰霜光环减速效果", "idc13",
            SkillTemplate.SkillType.AURA, createParams("aura_range", 5, "slowness_level", 2),
            this::executeFrostAura
        ));

        // IDC14 - 变异僵尸3（3个技能）
        registerSkillTemplate(new SkillTemplate(
            "idc14_particle_gun", "粒子机枪", "连续发射粒子攻击", "idc14",
            SkillTemplate.SkillType.PERIODIC, createParams("gun_interval", 4000, "particle_damage", 4.0),
            this::executeParticleGun
        ));
        registerSkillTemplate(new SkillTemplate(
            "idc14_smoke_particle", "烟雾粒子", "释放烟雾粒子遮挡视线", "idc14",
            SkillTemplate.SkillType.PERIODIC, createParams("smoke_interval", 12000, "smoke_duration", 8000),
            this::executeSmokeParticle
        ));
        registerSkillTemplate(new SkillTemplate(
            "idc14_dash_attack", "冲刺攻击", "快速冲刺攻击", "idc14",
            SkillTemplate.SkillType.PERIODIC, createParams("dash_interval", 10000, "dash_damage", 12.0),
            this::executeDashAttack
        ));

        // IDC15 - 鲜血猪灵（3个技能）
        registerSkillTemplate(new SkillTemplate(
            "idc15_red_particle_ball", "红色粒子球", "发射红色粒子球攻击", "idc15",
            SkillTemplate.SkillType.PERIODIC, createParams("ball_interval", 8000, "ball_damage", 10.0),
            this::executeRedParticleBall
        ));
        registerSkillTemplate(new SkillTemplate(
            "idc15_attack_heal", "攻击回血", "攻击时回复生命值", "idc15",
            SkillTemplate.SkillType.TRIGGER, createParams("heal_amount", 5.0, "heal_chance", 0.8),
            this::executeAttackHeal
        ));
        registerSkillTemplate(new SkillTemplate(
            "idc15_anti_piglin", "防猪人化", "防止被转化为猪人", "idc15",
            SkillTemplate.SkillType.PASSIVE, createParams("immunity", true),
            this::executeAntiPiglin
        ));

        // IDC16 - 暗影潜影贝（3个技能）
        registerSkillTemplate(new SkillTemplate(
            "idc16_shulker_bullet", "潜影贝子弹", "发射潜影贝子弹", "idc16",
            SkillTemplate.SkillType.PERIODIC, createParams("bullet_interval", 7000, "bullet_damage", 8.0),
            this::executeShulkerBullet
        ));
        registerSkillTemplate(new SkillTemplate(
            "idc16_inventory_chaos", "物品栏混乱", "混乱玩家物品栏", "idc16",
            SkillTemplate.SkillType.PERIODIC, createParams("chaos_interval", 20000, "chaos_duration", 6000),
            this::executeInventoryChaos
        ));
        registerSkillTemplate(new SkillTemplate(
            "idc16_invisibility", "隐身能力", "定期隐身", "idc16",
            SkillTemplate.SkillType.PERIODIC, createParams("invis_interval", 15000, "invis_duration", 5000),
            this::executeInvisibility
        ));

        // IDC17 - 变异雪傀儡（2个技能）
        registerSkillTemplate(new SkillTemplate(
            "idc17_snowball_attack", "雪球攻击", "发射雪球攻击", "idc17",
            SkillTemplate.SkillType.PERIODIC, createParams("snowball_interval", 5000, "snowball_damage", 6.0),
            this::executeSnowballAttack
        ));
        registerSkillTemplate(new SkillTemplate(
            "idc17_force_hostile", "强制敌对", "强制所有生物敌对", "idc17",
            SkillTemplate.SkillType.AURA, createParams("hostile_range", 10, "hostile_duration", 10000),
            this::executeForceHostile
        ));

        // IDC18 - 变异铁傀儡（3个技能）
        registerSkillTemplate(new SkillTemplate(
            "idc18_iron_throw", "铁块投掷", "投掷铁块攻击", "idc18",
            SkillTemplate.SkillType.PERIODIC, createParams("throw_interval", 8000, "iron_damage", 12.0),
            this::executeIronThrow
        ));
        registerSkillTemplate(new SkillTemplate(
            "idc18_ground_slam", "地面重击", "重击地面造成范围伤害", "idc18",
            SkillTemplate.SkillType.PERIODIC, createParams("slam_interval", 15000, "slam_damage", 15.0, "slam_range", 5),
            this::executeGroundSlam
        ));
        registerSkillTemplate(new SkillTemplate(
            "idc18_defense_buff", "防御增益", "提高防御能力", "idc18",
            SkillTemplate.SkillType.PASSIVE, createParams("defense_multiplier", 1.5, "knockback_resistance", 0.8),
            this::executeDefenseBuff
        ));

        // IDC19 - 变异僵尸Max（5个技能）
        registerSkillTemplate(new SkillTemplate(
            "idc19_multi_summon", "多重召唤", "召唤多种类型怪物", "idc19",
            SkillTemplate.SkillType.PERIODIC, createParams("summon_interval", 18000, "summon_types", 4),
            this::executeMultiSummon
        ));
        registerSkillTemplate(new SkillTemplate(
            "idc19_damage_summon", "受伤召唤", "受伤时召唤援兵", "idc19",
            SkillTemplate.SkillType.TRIGGER, createParams("damage_threshold", 40, "summon_count", 3),
            this::executeDamageSummon
        ));
        registerSkillTemplate(new SkillTemplate(
            "idc19_ranged_attack", "远程攻击", "多种远程攻击组合", "idc19",
            SkillTemplate.SkillType.PERIODIC, createParams("attack_interval", 6000, "attack_types", 3),
            this::executeRangedAttack
        ));
        registerSkillTemplate(new SkillTemplate(
            "idc19_electric_attack", "电击攻击", "强力电击攻击", "idc19",
            SkillTemplate.SkillType.PERIODIC, createParams("electric_interval", 10000, "electric_damage", 18.0),
            this::executeElectricAttack
        ));
        registerSkillTemplate(new SkillTemplate(
            "idc19_attack_buff", "攻击增益", "攻击时获得强化buff", "idc19",
            SkillTemplate.SkillType.TRIGGER, createParams("buff_duration", 15000, "strength_level", 3),
            this::executeAttackBuff
        ));

        // IDC20 - 灵魂坚守者（4个技能）
        registerSkillTemplate(new SkillTemplate(
            "idc20_soul_drain", "灵魂汲取", "汲取玩家灵魂回复生命", "idc20",
            SkillTemplate.SkillType.PERIODIC, createParams("drain_interval", 12000, "drain_amount", 8.0, "heal_amount", 6.0),
            this::executeSoulDrain
        ));
        registerSkillTemplate(new SkillTemplate(
            "idc20_soul_shield", "灵魂护盾", "灵魂护盾减少伤害", "idc20",
            SkillTemplate.SkillType.PASSIVE, createParams("damage_reduction", 0.3, "shield_duration", 20000),
            this::executeSoulShield
        ));
        registerSkillTemplate(new SkillTemplate(
            "idc20_soul_explosion", "灵魂爆炸", "死亡时释放灵魂爆炸", "idc20",
            SkillTemplate.SkillType.TRIGGER, createParams("explosion_damage", 20.0, "explosion_range", 8),
            this::executeSoulExplosion
        ));
        registerSkillTemplate(new SkillTemplate(
            "idc20_soul_summon", "灵魂召唤", "召唤灵魂守卫", "idc20",
            SkillTemplate.SkillType.PERIODIC, createParams("summon_interval", 25000, "soul_count", 2),
            this::executeSoulSummon
        ));

        // IDC21 - 凋零领主（6个技能）
        registerSkillTemplate(new SkillTemplate(
            "idc21_wither_skull", "凋零头颅", "发射凋零头颅", "idc21",
            SkillTemplate.SkillType.PERIODIC, createParams("skull_interval", 8000, "skull_damage", 15.0),
            this::executeWitherSkull
        ));
        registerSkillTemplate(new SkillTemplate(
            "idc21_wither_aura", "凋零光环", "凋零效果光环", "idc21",
            SkillTemplate.SkillType.AURA, createParams("aura_range", 6, "wither_level", 2),
            this::executeWitherAura
        ));
        registerSkillTemplate(new SkillTemplate(
            "idc21_summon_skeletons", "召唤骷髅", "召唤凋零骷髅", "idc21",
            SkillTemplate.SkillType.PERIODIC, createParams("summon_interval", 20000, "skeleton_count", 3),
            this::executeSummonSkeletons
        ));
        registerSkillTemplate(new SkillTemplate(
            "idc21_dash_attack", "冲刺攻击", "快速冲刺攻击", "idc21",
            SkillTemplate.SkillType.PERIODIC, createParams("dash_interval", 15000, "dash_damage", 20.0),
            this::executeDashAttack
        ));
        registerSkillTemplate(new SkillTemplate(
            "idc21_regeneration", "生命恢复", "持续恢复生命值", "idc21",
            SkillTemplate.SkillType.PASSIVE, createParams("regen_amount", 2.0, "regen_interval", 3000),
            this::executeRegeneration
        ));
        registerSkillTemplate(new SkillTemplate(
            "idc21_explosion_immunity", "爆炸免疫", "免疫爆炸伤害", "idc21",
            SkillTemplate.SkillType.PASSIVE, createParams("explosion_immunity", true),
            this::executeExplosionImmunity
        ));

        // IDC22 - 异变之王（10个技能）
        registerSkillTemplate(new SkillTemplate(
            "idc22_obsidian_pillar", "黑曜石柱", "生成黑曜石柱", "idc22",
            SkillTemplate.SkillType.PERIODIC, createParams("pillar_interval", 20000, "pillar_count", 4),
            this::executeObsidianPillar
        ));
        registerSkillTemplate(new SkillTemplate(
            "idc22_obsidian_attack", "黑曜石攻击", "黑曜石块攻击", "idc22",
            SkillTemplate.SkillType.PERIODIC, createParams("attack_interval", 8000, "attack_damage", 12.0),
            this::executeObsidianAttack
        ));
        registerSkillTemplate(new SkillTemplate(
            "idc22_crystal_attack", "末影水晶攻击", "末影水晶爆炸攻击", "idc22",
            SkillTemplate.SkillType.PERIODIC, createParams("crystal_interval", 15000, "crystal_damage", 25.0),
            this::executeCrystalAttack
        ));
        registerSkillTemplate(new SkillTemplate(
            "idc22_boss_bar", "Boss血条", "显示Boss血条", "idc22",
            SkillTemplate.SkillType.PASSIVE, createParams("bar_color", "PURPLE", "bar_style", "SOLID"),
            this::executeBossBar
        ));
        registerSkillTemplate(new SkillTemplate(
            "idc22_damage_reduction", "伤害减免", "减少受到的伤害", "idc22",
            SkillTemplate.SkillType.PASSIVE, createParams("damage_reduction", 0.5),
            this::executeDamageReduction
        ));
        registerSkillTemplate(new SkillTemplate(
            "idc22_knockback_immunity", "击退免疫", "免疫击退效果", "idc22",
            SkillTemplate.SkillType.PASSIVE, createParams("knockback_immunity", true),
            this::executeKnockbackImmunity
        ));
        registerSkillTemplate(new SkillTemplate(
            "idc22_fire_immunity", "火焰免疫", "免疫火焰伤害", "idc22",
            SkillTemplate.SkillType.PASSIVE, createParams("fire_immunity", true),
            this::executeFireImmunity
        ));
        registerSkillTemplate(new SkillTemplate(
            "idc22_regeneration", "生命恢复", "强力生命恢复", "idc22",
            SkillTemplate.SkillType.PASSIVE, createParams("regen_amount", 5.0, "regen_interval", 2000),
            this::executeRegeneration
        ));
        registerSkillTemplate(new SkillTemplate(
            "idc22_teleport_escape", "瞬移逃脱", "低血量时瞬移逃脱", "idc22",
            SkillTemplate.SkillType.TRIGGER, createParams("health_threshold", 0.2, "teleport_range", 20),
            this::executeTeleportEscape
        ));
        registerSkillTemplate(new SkillTemplate(
            "idc22_final_explosion", "终极爆炸", "死亡时的终极爆炸", "idc22",
            SkillTemplate.SkillType.TRIGGER, createParams("explosion_power", 8.0, "explosion_damage", 50.0),
            this::executeFinalExplosion
        ));

        logger.info("已注册 IDC 系列技能模板（共50个技能）");
    }
    
    /**
     * 注册技能模板
     *
     * @param template 技能模板
     */
    public void registerSkillTemplate(SkillTemplate template) {
        skillTemplates.put(template.getId(), template);
    }

    /**
     * 创建参数映射的辅助方法
     *
     * @param keyValues 键值对
     * @return 参数映射
     */
    private Map<String, Object> createParams(Object... keyValues) {
        Map<String, Object> params = new HashMap<>();
        for (int i = 0; i < keyValues.length; i += 2) {
            if (i + 1 < keyValues.length) {
                params.put(keyValues[i].toString(), keyValues[i + 1]);
            }
        }
        return params;
    }
    
    /**
     * 启用技能
     * 
     * @param entity 实体
     * @param skillId 技能ID
     * @param parameters 技能参数
     */
    public void enableSkill(LivingEntity entity, String skillId, Map<String, Object> parameters) {
        SkillTemplate template = skillTemplates.get(skillId);
        if (template == null) {
            logger.warning("未找到技能模板: " + skillId);
            return;
        }
        
        try {
            // 合并默认参数和自定义参数
            Map<String, Object> finalParams = new HashMap<>(template.getDefaultParameters());
            if (parameters != null) {
                finalParams.putAll(parameters);
            }
            
            // 执行技能
            template.getExecutor().execute(entity, finalParams);
            
            // 记录活跃技能
            activeSkills.computeIfAbsent(entity, k -> new HashSet<>()).add(skillId);
            
            logger.info("为实体启用技能: " + skillId + " (" + template.getName() + ")");
            
        } catch (Exception e) {
            logger.warning("启用技能失败: " + skillId + " - " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 禁用实体的所有技能
     * 
     * @param entity 实体
     */
    public void disableAllSkills(LivingEntity entity) {
        Set<String> skills = activeSkills.remove(entity);
        if (skills != null && !skills.isEmpty()) {
            logger.info("禁用实体的所有技能: " + skills.size() + " 个");
        }
    }
    
    /**
     * 获取技能模板
     * 
     * @param skillId 技能ID
     * @return 技能模板
     */
    public SkillTemplate getSkillTemplate(String skillId) {
        return skillTemplates.get(skillId);
    }
    
    /**
     * 获取所有技能模板
     * 
     * @return 技能模板映射
     */
    public Map<String, SkillTemplate> getAllSkillTemplates() {
        return new HashMap<>(skillTemplates);
    }
    
    /**
     * 获取按来源分组的技能模板
     * 
     * @return 按来源分组的技能模板
     */
    public Map<String, List<SkillTemplate>> getSkillTemplatesBySource() {
        Map<String, List<SkillTemplate>> grouped = new HashMap<>();
        
        for (SkillTemplate template : skillTemplates.values()) {
            grouped.computeIfAbsent(template.getSourceEntity(), k -> new ArrayList<>()).add(template);
        }
        
        return grouped;
    }
    
    // 以下是技能执行方法的占位符实现
    // 实际实现需要根据现有的技能系统进行集成
    
    private void executePoisonAttack(LivingEntity entity, Map<String, Object> parameters) {
        // TODO: 实现剧毒攻击逻辑
        logger.info("执行剧毒攻击技能");
    }
    
    private void executeDeathSummon(LivingEntity entity, Map<String, Object> parameters) {
        // TODO: 实现死亡召唤逻辑
        logger.info("执行死亡召唤技能");
    }
    
    private void executeArrowAttack(LivingEntity entity, Map<String, Object> parameters) {
        // TODO: 实现箭矢攻击逻辑
        logger.info("执行箭矢攻击技能");
    }
    
    private void executeMageSummon(LivingEntity entity, Map<String, Object> parameters) {
        // TODO: 实现法师召唤逻辑
        logger.info("执行法师召唤技能");
    }
    
    private void executeExplodeAttack(LivingEntity entity, Map<String, Object> parameters) {
        // TODO: 实现自爆攻击逻辑
        logger.info("执行自爆攻击技能");
    }
    
    private void executePoisonArrow(LivingEntity entity, Map<String, Object> parameters) {
        // TODO: 实现毒箭攻击逻辑
        logger.info("执行毒箭攻击技能");
    }
    
    private void executeLightningAttack(LivingEntity entity, Map<String, Object> parameters) {
        // TODO: 实现电击攻击逻辑
        logger.info("执行电击攻击技能");
    }
    
    private void executeFreezeAttack(LivingEntity entity, Map<String, Object> parameters) {
        // TODO: 实现冰冻攻击逻辑
        logger.info("执行冰冻攻击技能");
    }
    
    private void executeTeleportAttack(LivingEntity entity, Map<String, Object> parameters) {
        // TODO: 实现传送攻击逻辑
        logger.info("执行传送攻击技能");
    }
    
    private void executeThunderAttack(LivingEntity entity, Map<String, Object> parameters) {
        // TODO: 实现雷霆攻击逻辑
        logger.info("执行雷霆攻击技能");
    }
    
    private void executeEnhancedPoison(LivingEntity entity, Map<String, Object> parameters) {
        // TODO: 实现增强剧毒攻击逻辑
        logger.info("执行增强剧毒攻击技能");
    }
    
    private void executeFlameAttack(LivingEntity entity, Map<String, Object> parameters) {
        // TODO: 实现烈焰攻击逻辑
        logger.info("执行烈焰攻击技能");
    }
    
    private void executeChargedLightning(LivingEntity entity, Map<String, Object> parameters) {
        // TODO: 实现充能闪电攻击逻辑
        logger.info("执行充能闪电攻击技能");
    }
    
    private void executeAreaElectric(LivingEntity entity, Map<String, Object> parameters) {
        // TODO: 实现范围电流逻辑
        logger.info("执行范围电流技能");
    }
    
    private void executeWebTrap(LivingEntity entity, Map<String, Object> parameters) {
        // TODO: 实现蜘蛛网陷阱逻辑
        logger.info("执行蜘蛛网陷阱技能");
    }
    
    private void executeGuardianSummon(LivingEntity entity, Map<String, Object> parameters) {
        // TODO: 实现卫道士召唤逻辑
        logger.info("执行卫道士召唤技能");
    }

    // ========== 新增技能执行方法占位符 ==========

    // ID系列新增技能
    private void executeDamageSummon(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行受伤召唤技能");
    }

    private void executeAreaDebuff(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行范围减益技能");
    }

    private void executeGlobalDamage(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行全局伤害技能");
    }

    private void executeAreaLightning(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行范围闪电技能");
    }

    private void executeAreaBuff(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行范围增益技能");
    }

    private void executeDetectSummon(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行检测召唤技能");
    }

    private void executeFlight(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行飞行效果技能");
    }

    private void executeDeathBalloons(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行死亡气球技能");
    }

    private void executeFogGeneration(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行迷雾生成技能");
    }

    private void executeDeathFog(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行死亡雾气技能");
    }

    private void executeGlobalLightning(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行全局雷电技能");
    }

    private void executeTeleportLightning(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行瞬移雷击技能");
    }

    private void executeSummonGuards(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行召唤护卫技能");
    }

    private void executeTimeControl(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行时间控制技能");
    }

    private void executeFreezeAll(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行全体冻结技能");
    }

    private void executeAttackBuff(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行攻击增益技能");
    }

    private void executeSummonMinions(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行召唤小兵技能");
    }

    private void executeAuraBuff(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行光环增益技能");
    }

    private void executeExplosion(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行守护爆炸技能");
    }

    private void executeContinuousSummon(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行持续召唤技能");
    }

    private void executeGlobalDebuff(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行全局减益技能");
    }

    private void executeRandomSummon(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行随机召唤技能");
    }

    private void executeRangedAttack(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行远程攻击技能");
    }

    private void executeElectricAttack(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行电击攻击技能");
    }

    private void executeSpecialSummon(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行特殊召唤技能");
    }

    // IDC系列新增技能
    private void executePoisonRelease(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行毒液释放技能");
    }

    private void executeSummonVindicators(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行召唤卫道士技能");
    }

    private void executeParticleBall(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行粒子球攻击技能");
    }

    private void executeAuraDamage(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行光环伤害技能");
    }

    private void executeDNASpiral(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行DNA螺旋魔法技能");
    }

    private void executeFangAttack(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行尖牙攻击技能");
    }

    private void executeCritAura(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行暴击光环技能");
    }

    private void executeChargeDamage(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行撞击伤害技能");
    }

    private void executeSummonArmed(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行召唤武装僵尸技能");
    }

    private void executeFireAura(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行火焰光环技能");
    }

    private void executeSummonZombies(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行召唤僵尸技能");
    }

    private void executeInventoryChaos(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行物品栏混乱技能");
    }

    private void executeTripleShot(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行三连射箭技能");
    }

    private void executeFrostAura(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行冰霜光环技能");
    }

    private void executeParticleGun(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行粒子机枪技能");
    }

    private void executeSmokeParticle(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行烟雾粒子技能");
    }

    private void executeDashAttack(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行冲刺攻击技能");
    }

    private void executeRedParticleBall(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行红色粒子球技能");
    }

    private void executeAttackHeal(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行攻击回血技能");
    }

    private void executeAntiPiglin(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行防猪人化技能");
    }

    private void executeShulkerBullet(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行潜影贝子弹技能");
    }

    private void executeInvisibility(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行隐身能力技能");
    }

    private void executeSnowballAttack(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行雪球攻击技能");
    }

    private void executeForceHostile(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行强制敌对技能");
    }

    private void executeIronThrow(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行铁块投掷技能");
    }

    private void executeGroundSlam(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行地面重击技能");
    }

    private void executeDefenseBuff(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行防御增益技能");
    }

    private void executeMultiSummon(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行多重召唤技能");
    }

    private void executeSoulDrain(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行灵魂汲取技能");
    }

    private void executeSoulShield(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行灵魂护盾技能");
    }

    private void executeSoulExplosion(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行灵魂爆炸技能");
    }

    private void executeSoulSummon(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行灵魂召唤技能");
    }

    private void executeWitherSkull(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行凋零头颅技能");
    }

    private void executeWitherAura(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行凋零光环技能");
    }

    private void executeSummonSkeletons(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行召唤骷髅技能");
    }

    private void executeRegeneration(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行生命恢复技能");
    }

    private void executeExplosionImmunity(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行爆炸免疫技能");
    }

    private void executeObsidianPillar(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行黑曜石柱技能");
    }

    private void executeObsidianAttack(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行黑曜石攻击技能");
    }

    private void executeCrystalAttack(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行末影水晶攻击技能");
    }

    private void executeBossBar(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行Boss血条技能");
    }

    private void executeDamageReduction(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行伤害减免技能");
    }

    private void executeKnockbackImmunity(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行击退免疫技能");
    }

    private void executeFireImmunity(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行火焰免疫技能");
    }

    private void executeTeleportEscape(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行瞬移逃脱技能");
    }

    private void executeFinalExplosion(LivingEntity entity, Map<String, Object> parameters) {
        logger.info("执行终极爆炸技能");
    }
}
