package org.Ver_zhzh.customZombie.UserMaker;

import java.util.ArrayList;
import java.util.List;

/**
 * 验证结果类
 * 用于存储配置验证的结果信息
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class ValidationResult {
    
    private boolean valid;                // 是否有效
    private List<String> errors;          // 错误信息列表
    private List<String> warnings;        // 警告信息列表
    private List<String> infos;           // 提示信息列表
    
    /**
     * 默认构造函数
     */
    public ValidationResult() {
        this.valid = true;
        this.errors = new ArrayList<>();
        this.warnings = new ArrayList<>();
        this.infos = new ArrayList<>();
    }
    
    /**
     * 添加错误信息
     * 
     * @param error 错误信息
     */
    public void addError(String error) {
        this.errors.add(error);
        this.valid = false;
    }
    
    /**
     * 添加警告信息
     * 
     * @param warning 警告信息
     */
    public void addWarning(String warning) {
        this.warnings.add(warning);
    }
    
    /**
     * 添加提示信息
     * 
     * @param info 提示信息
     */
    public void addInfo(String info) {
        this.infos.add(info);
    }
    
    /**
     * 检查是否有错误
     * 
     * @return 是否有错误
     */
    public boolean hasErrors() {
        return !errors.isEmpty();
    }
    
    /**
     * 检查是否有警告
     * 
     * @return 是否有警告
     */
    public boolean hasWarnings() {
        return !warnings.isEmpty();
    }
    
    /**
     * 检查是否有提示
     * 
     * @return 是否有提示
     */
    public boolean hasInfos() {
        return !infos.isEmpty();
    }
    
    /**
     * 获取所有消息的总数
     * 
     * @return 消息总数
     */
    public int getTotalMessageCount() {
        return errors.size() + warnings.size() + infos.size();
    }
    
    /**
     * 合并另一个验证结果
     * 
     * @param other 另一个验证结果
     */
    public void merge(ValidationResult other) {
        if (other != null) {
            this.errors.addAll(other.errors);
            this.warnings.addAll(other.warnings);
            this.infos.addAll(other.infos);
            if (!other.valid) {
                this.valid = false;
            }
        }
    }
    
    /**
     * 清空所有消息
     */
    public void clear() {
        this.errors.clear();
        this.warnings.clear();
        this.infos.clear();
        this.valid = true;
    }
    
    /**
     * 获取格式化的错误信息
     * 
     * @return 格式化的错误信息
     */
    public String getFormattedErrors() {
        if (errors.isEmpty()) {
            return "";
        }
        
        StringBuilder sb = new StringBuilder();
        sb.append("错误信息:\n");
        for (int i = 0; i < errors.size(); i++) {
            sb.append("  ").append(i + 1).append(". ").append(errors.get(i)).append("\n");
        }
        return sb.toString();
    }
    
    /**
     * 获取格式化的警告信息
     * 
     * @return 格式化的警告信息
     */
    public String getFormattedWarnings() {
        if (warnings.isEmpty()) {
            return "";
        }
        
        StringBuilder sb = new StringBuilder();
        sb.append("警告信息:\n");
        for (int i = 0; i < warnings.size(); i++) {
            sb.append("  ").append(i + 1).append(". ").append(warnings.get(i)).append("\n");
        }
        return sb.toString();
    }
    
    /**
     * 获取格式化的提示信息
     * 
     * @return 格式化的提示信息
     */
    public String getFormattedInfos() {
        if (infos.isEmpty()) {
            return "";
        }
        
        StringBuilder sb = new StringBuilder();
        sb.append("提示信息:\n");
        for (int i = 0; i < infos.size(); i++) {
            sb.append("  ").append(i + 1).append(". ").append(infos.get(i)).append("\n");
        }
        return sb.toString();
    }
    
    /**
     * 获取完整的格式化消息
     * 
     * @return 完整的格式化消息
     */
    public String getFormattedMessage() {
        StringBuilder sb = new StringBuilder();
        
        if (hasErrors()) {
            sb.append(getFormattedErrors());
        }
        
        if (hasWarnings()) {
            if (sb.length() > 0) {
                sb.append("\n");
            }
            sb.append(getFormattedWarnings());
        }
        
        if (hasInfos()) {
            if (sb.length() > 0) {
                sb.append("\n");
            }
            sb.append(getFormattedInfos());
        }
        
        return sb.toString();
    }
    
    /**
     * 获取简短的状态描述
     * 
     * @return 状态描述
     */
    public String getStatusDescription() {
        if (valid && !hasWarnings() && !hasInfos()) {
            return "验证通过";
        } else if (valid && hasWarnings()) {
            return String.format("验证通过（%d个警告）", warnings.size());
        } else {
            return String.format("验证失败（%d个错误）", errors.size());
        }
    }
    
    // Getter和Setter方法
    public boolean isValid() { return valid; }
    public void setValid(boolean valid) { this.valid = valid; }
    
    public List<String> getErrors() { return errors; }
    public void setErrors(List<String> errors) { this.errors = errors; }
    
    public List<String> getWarnings() { return warnings; }
    public void setWarnings(List<String> warnings) { this.warnings = warnings; }
    
    public List<String> getInfos() { return infos; }
    public void setInfos(List<String> infos) { this.infos = infos; }
    
    @Override
    public String toString() {
        return String.format("ValidationResult{valid=%s, errors=%d, warnings=%d, infos=%d}", 
                           valid, errors.size(), warnings.size(), infos.size());
    }
}
