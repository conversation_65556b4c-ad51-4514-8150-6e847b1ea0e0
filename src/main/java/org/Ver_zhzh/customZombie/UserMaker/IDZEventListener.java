package org.Ver_zhzh.customZombie.UserMaker;

import org.bukkit.ChatColor;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.entity.EntityDeathEvent;
import org.bukkit.event.entity.EntitySpawnEvent;
import org.bukkit.event.entity.EntityTargetEvent;
import org.bukkit.plugin.Plugin;

import java.util.logging.Logger;

/**
 * IDZ事件监听器
 * 处理IDZ自定义怪物的特殊事件
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class IDZEventListener implements Listener {
    
    private final Plugin plugin;
    private final Logger logger;
    private final IDZEntityManager entityManager;
    private final boolean debugMode;
    
    /**
     * 构造函数
     * 
     * @param plugin 插件实例
     * @param entityManager IDZ实体管理器
     * @param debugMode 是否开启调试模式
     */
    public IDZEventListener(Plugin plugin, IDZEntityManager entityManager, boolean debugMode) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.entityManager = entityManager;
        this.debugMode = debugMode;
    }
    
    /**
     * 处理IDZ怪物死亡事件
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onIDZMonsterDeath(EntityDeathEvent event) {
        LivingEntity entity = event.getEntity();
        
        // 检查是否为IDZ自定义怪物
        if (!entityManager.isIDZCustomMonster(entity)) {
            return;
        }
        
        try {
            String monsterId = entityManager.getIDZMonsterId(entity);
            String monsterName = entityManager.getIDZMonsterName(entity);
            
            if (debugMode) {
                logger.info("IDZ怪物死亡: " + monsterName + " (" + monsterId + ")");
            }
            
            // 获取击杀者
            Player killer = event.getEntity().getKiller();
            if (killer != null) {
                // 发送击杀消息
                killer.sendMessage(ChatColor.GREEN + "你击杀了IDZ自定义怪物: " + ChatColor.GOLD + monsterName);
                
                if (debugMode) {
                    killer.sendMessage(ChatColor.GRAY + "怪物ID: " + monsterId);
                }
            }
            
            // 处理怪物死亡逻辑
            entityManager.handleMonsterDeath(entity);
            
            // 播放死亡粒子效果
            CustomMonster monster = entityManager.getCustomMonster(entity);
            if (monster != null) {
                ParticleEffect deathEffect = monster.getParticleEffects().get("death");
                if (deathEffect != null) {
                    deathEffect.play(entity.getLocation());
                }
            }
            
        } catch (Exception e) {
            logger.warning("处理IDZ怪物死亡事件失败: " + e.getMessage());
            if (debugMode) {
                e.printStackTrace();
            }
        }
    }
    
    /**
     * 处理IDZ怪物攻击事件
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onIDZMonsterAttack(EntityDamageByEntityEvent event) {
        // 检查攻击者是否为IDZ怪物
        if (!(event.getDamager() instanceof LivingEntity)) {
            return;
        }
        
        LivingEntity attacker = (LivingEntity) event.getDamager();
        if (!entityManager.isIDZCustomMonster(attacker)) {
            return;
        }
        
        try {
            String monsterId = entityManager.getIDZMonsterId(attacker);
            
            if (debugMode) {
                logger.info("IDZ怪物攻击: " + monsterId + " 攻击 " + event.getEntity().getType());
            }
            
            // 播放攻击粒子效果
            CustomMonster monster = entityManager.getCustomMonster(attacker);
            if (monster != null) {
                ParticleEffect attackEffect = monster.getParticleEffects().get("attack");
                if (attackEffect != null) {
                    attackEffect.play(attacker.getLocation());
                }
            }
            
            // 这里可以添加特殊攻击效果的处理
            // 例如剧毒攻击、冰冻攻击等
            
        } catch (Exception e) {
            logger.warning("处理IDZ怪物攻击事件失败: " + e.getMessage());
            if (debugMode) {
                e.printStackTrace();
            }
        }
    }
    
    /**
     * 处理IDZ怪物受伤事件
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onIDZMonsterHurt(EntityDamageByEntityEvent event) {
        // 检查受伤者是否为IDZ怪物
        if (!(event.getEntity() instanceof LivingEntity)) {
            return;
        }
        
        LivingEntity victim = (LivingEntity) event.getEntity();
        if (!entityManager.isIDZCustomMonster(victim)) {
            return;
        }
        
        try {
            String monsterId = entityManager.getIDZMonsterId(victim);
            
            if (debugMode) {
                logger.info("IDZ怪物受伤: " + monsterId + " 受到 " + event.getDamage() + " 点伤害");
            }
            
            // 播放受伤粒子效果
            CustomMonster monster = entityManager.getCustomMonster(victim);
            if (monster != null) {
                ParticleEffect hurtEffect = monster.getParticleEffects().get("hurt");
                if (hurtEffect != null) {
                    hurtEffect.play(victim.getLocation());
                }
            }
            
            // 这里可以添加受伤时的特殊效果
            // 例如反击、召唤援兵等
            
        } catch (Exception e) {
            logger.warning("处理IDZ怪物受伤事件失败: " + e.getMessage());
            if (debugMode) {
                e.printStackTrace();
            }
        }
    }
    
    /**
     * 处理IDZ怪物生成事件
     */
    @EventHandler(priority = EventPriority.MONITOR)
    public void onIDZMonsterSpawn(EntitySpawnEvent event) {
        // 检查是否为IDZ怪物
        if (!(event.getEntity() instanceof LivingEntity)) {
            return;
        }
        
        LivingEntity entity = (LivingEntity) event.getEntity();
        if (!entityManager.isIDZCustomMonster(entity)) {
            return;
        }
        
        try {
            String monsterId = entityManager.getIDZMonsterId(entity);
            String monsterName = entityManager.getIDZMonsterName(entity);
            
            if (debugMode) {
                logger.info("IDZ怪物生成: " + monsterName + " (" + monsterId + ") 在 " + 
                           entity.getLocation().getWorld().getName() + " " +
                           entity.getLocation().getBlockX() + "," + 
                           entity.getLocation().getBlockY() + "," + 
                           entity.getLocation().getBlockZ());
            }
            
            // 播放生成粒子效果
            CustomMonster monster = entityManager.getCustomMonster(entity);
            if (monster != null) {
                ParticleEffect spawnEffect = monster.getParticleEffects().get("spawn");
                if (spawnEffect != null) {
                    // 延迟播放，确保实体完全生成
                    plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
                        spawnEffect.play(entity.getLocation());
                    }, 1L);
                }
            }
            
        } catch (Exception e) {
            logger.warning("处理IDZ怪物生成事件失败: " + e.getMessage());
            if (debugMode) {
                e.printStackTrace();
            }
        }
    }
    
    /**
     * 处理IDZ怪物目标选择事件
     */
    @EventHandler(priority = EventPriority.HIGH)
    public void onIDZMonsterTarget(EntityTargetEvent event) {
        // 检查是否为IDZ怪物
        if (!(event.getEntity() instanceof LivingEntity)) {
            return;
        }
        
        LivingEntity entity = (LivingEntity) event.getEntity();
        if (!entityManager.isIDZCustomMonster(entity)) {
            return;
        }
        
        try {
            String monsterId = entityManager.getIDZMonsterId(entity);
            
            if (debugMode && event.getTarget() != null) {
                logger.info("IDZ怪物目标: " + monsterId + " 锁定目标 " + event.getTarget().getType());
            }
            
            // 这里可以添加特殊的目标选择逻辑
            // 例如优先攻击特定玩家、忽略某些实体等
            
        } catch (Exception e) {
            logger.warning("处理IDZ怪物目标事件失败: " + e.getMessage());
            if (debugMode) {
                e.printStackTrace();
            }
        }
    }
    
    /**
     * 获取统计信息
     * 
     * @return 统计信息字符串
     */
    public String getStatistics() {
        return "IDZ事件监听器统计: 调试模式=" + (debugMode ? "开启" : "关闭");
    }
}
