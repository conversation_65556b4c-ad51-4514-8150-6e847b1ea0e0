package org.Ver_zhzh.customZombie.UserMaker;

import java.util.*;
import java.util.logging.Logger;

/**
 * 技能统计工具
 * 用于统计和验证IDZ系统的技能模板数量
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class SkillStatistics {
    
    private final SkillTemplateManager skillManager;
    private final Logger logger;
    
    /**
     * 构造函数
     * 
     * @param skillManager 技能模板管理器
     * @param logger 日志记录器
     */
    public SkillStatistics(SkillTemplateManager skillManager, Logger logger) {
        this.skillManager = skillManager;
        this.logger = logger;
    }
    
    /**
     * 生成详细的技能统计报告
     * 
     * @return 统计报告字符串
     */
    public String generateDetailedReport() {
        StringBuilder report = new StringBuilder();
        
        var allSkills = skillManager.getAllSkillTemplates();
        var skillsBySource = skillManager.getSkillTemplatesBySource();
        
        report.append("========== IDZ技能模板统计报告 ==========\n");
        report.append("总技能数量: ").append(allSkills.size()).append(" 个\n\n");
        
        // ID系列统计
        report.append("=== ID系列技能统计 ===\n");
        int totalIdSkills = 0;
        Map<String, Integer> idSourceCount = new TreeMap<>();
        
        for (var entry : skillsBySource.entrySet()) {
            String source = entry.getKey();
            if (source.startsWith("id") && !source.startsWith("idc")) {
                int count = entry.getValue().size();
                idSourceCount.put(source, count);
                totalIdSkills += count;
            }
        }
        
        for (var entry : idSourceCount.entrySet()) {
            report.append("  ").append(entry.getKey().toUpperCase()).append(": ").append(entry.getValue()).append(" 个技能\n");
        }
        report.append("ID系列总计: ").append(totalIdSkills).append(" 个技能\n\n");
        
        // IDC系列统计
        report.append("=== IDC系列技能统计 ===\n");
        int totalIdcSkills = 0;
        Map<String, Integer> idcSourceCount = new TreeMap<>();
        
        for (var entry : skillsBySource.entrySet()) {
            String source = entry.getKey();
            if (source.startsWith("idc")) {
                int count = entry.getValue().size();
                idcSourceCount.put(source, count);
                totalIdcSkills += count;
            }
        }
        
        for (var entry : idcSourceCount.entrySet()) {
            report.append("  ").append(entry.getKey().toUpperCase()).append(": ").append(entry.getValue()).append(" 个技能\n");
        }
        report.append("IDC系列总计: ").append(totalIdcSkills).append(" 个技能\n\n");
        
        // 技能类型统计
        report.append("=== 技能类型统计 ===\n");
        Map<SkillTemplateManager.SkillTemplate.SkillType, Integer> typeCount = new HashMap<>();
        
        for (var skill : allSkills.values()) {
            typeCount.merge(skill.getType(), 1, Integer::sum);
        }
        
        for (var entry : typeCount.entrySet()) {
            report.append("  ").append(entry.getKey()).append(": ").append(entry.getValue()).append(" 个\n");
        }
        
        // 验证目标数量
        report.append("\n=== 目标验证 ===\n");
        report.append("ID系列目标: 35 个，实际: ").append(totalIdSkills).append(" 个");
        if (totalIdSkills == 35) {
            report.append(" ✓\n");
        } else {
            report.append(" ✗ (差异: ").append(35 - totalIdSkills).append(")\n");
        }
        
        report.append("IDC系列目标: 50 个，实际: ").append(totalIdcSkills).append(" 个");
        if (totalIdcSkills == 50) {
            report.append(" ✓\n");
        } else {
            report.append(" ✗ (差异: ").append(50 - totalIdcSkills).append(")\n");
        }
        
        report.append("总计目标: 85 个，实际: ").append(totalIdSkills + totalIdcSkills).append(" 个");
        if (totalIdSkills + totalIdcSkills == 85) {
            report.append(" ✓\n");
        } else {
            report.append(" ✗ (差异: ").append(85 - (totalIdSkills + totalIdcSkills)).append(")\n");
        }
        
        report.append("==========================================");
        
        return report.toString();
    }
    
    /**
     * 生成简要统计报告
     * 
     * @return 简要报告字符串
     */
    public String generateSummaryReport() {
        var allSkills = skillManager.getAllSkillTemplates();
        var skillsBySource = skillManager.getSkillTemplatesBySource();
        
        int totalIdSkills = 0;
        int totalIdcSkills = 0;
        
        for (var entry : skillsBySource.entrySet()) {
            String source = entry.getKey();
            int count = entry.getValue().size();
            
            if (source.startsWith("id") && !source.startsWith("idc")) {
                totalIdSkills += count;
            } else if (source.startsWith("idc")) {
                totalIdcSkills += count;
            }
        }
        
        return String.format("IDZ技能统计: ID系列=%d个, IDC系列=%d个, 总计=%d个", 
                           totalIdSkills, totalIdcSkills, totalIdSkills + totalIdcSkills);
    }
    
    /**
     * 验证技能数量是否符合预期
     * 
     * @return 验证结果
     */
    public boolean validateSkillCounts() {
        var skillsBySource = skillManager.getSkillTemplatesBySource();
        
        int totalIdSkills = 0;
        int totalIdcSkills = 0;
        
        for (var entry : skillsBySource.entrySet()) {
            String source = entry.getKey();
            int count = entry.getValue().size();
            
            if (source.startsWith("id") && !source.startsWith("idc")) {
                totalIdSkills += count;
            } else if (source.startsWith("idc")) {
                totalIdcSkills += count;
            }
        }
        
        boolean idValid = totalIdSkills == 35;
        boolean idcValid = totalIdcSkills == 50;
        boolean totalValid = (totalIdSkills + totalIdcSkills) == 85;
        
        if (!idValid) {
            logger.warning("ID系列技能数量不符合预期: 期望35个, 实际" + totalIdSkills + "个");
        }
        
        if (!idcValid) {
            logger.warning("IDC系列技能数量不符合预期: 期望50个, 实际" + totalIdcSkills + "个");
        }
        
        if (!totalValid) {
            logger.warning("总技能数量不符合预期: 期望85个, 实际" + (totalIdSkills + totalIdcSkills) + "个");
        }
        
        return idValid && idcValid && totalValid;
    }
    
    /**
     * 列出所有技能ID
     * 
     * @return 技能ID列表
     */
    public List<String> getAllSkillIds() {
        return new ArrayList<>(skillManager.getAllSkillTemplates().keySet());
    }
    
    /**
     * 按来源分组列出技能ID
     * 
     * @return 按来源分组的技能ID
     */
    public Map<String, List<String>> getSkillIdsBySource() {
        Map<String, List<String>> result = new HashMap<>();
        var skillsBySource = skillManager.getSkillTemplatesBySource();
        
        for (var entry : skillsBySource.entrySet()) {
            String source = entry.getKey();
            List<String> skillIds = new ArrayList<>();
            
            for (var skill : entry.getValue()) {
                skillIds.add(skill.getId());
            }
            
            result.put(source, skillIds);
        }
        
        return result;
    }
    
    /**
     * 查找缺失的技能
     * 
     * @return 缺失技能的报告
     */
    public String findMissingSkills() {
        StringBuilder report = new StringBuilder();
        
        // 检查ID系列应有的技能源
        String[] expectedIdSources = {"id5", "id6", "id7", "id10", "id11", "id12", "id13", "id14", "id15", "id17", 
                                     "id18", "id19", "id20", "id21", "id22", "id23", "id24", "id25"};
        
        // 检查IDC系列应有的技能源
        String[] expectedIdcSources = {"idc1", "idc3", "idc4", "idc5", "idc6", "idc7", "idc8", "idc9", "idc10",
                                      "idc11", "idc12", "idc13", "idc14", "idc15", "idc16", "idc17", "idc18",
                                      "idc19", "idc20", "idc21", "idc22"};
        
        var skillsBySource = skillManager.getSkillTemplatesBySource();
        
        report.append("=== 缺失技能源检查 ===\n");
        
        // 检查ID系列
        for (String expectedSource : expectedIdSources) {
            if (!skillsBySource.containsKey(expectedSource)) {
                report.append("缺失ID技能源: ").append(expectedSource).append("\n");
            }
        }
        
        // 检查IDC系列
        for (String expectedSource : expectedIdcSources) {
            if (!skillsBySource.containsKey(expectedSource)) {
                report.append("缺失IDC技能源: ").append(expectedSource).append("\n");
            }
        }
        
        if (report.length() == "=== 缺失技能源检查 ===\n".length()) {
            report.append("所有预期的技能源都已找到 ✓\n");
        }
        
        return report.toString();
    }
    
    /**
     * 输出统计信息到日志
     */
    public void logStatistics() {
        logger.info("========== IDZ技能统计 ==========");
        logger.info(generateSummaryReport());
        
        if (validateSkillCounts()) {
            logger.info("技能数量验证通过 ✓");
        } else {
            logger.warning("技能数量验证失败 ✗");
        }
        
        logger.info("================================");
    }
}
