package org.Ver_zhzh.customZombie.UserMaker;

import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;

/**
 * 药水效果配置类
 * 用于配置自定义怪物的药水效果
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class PotionEffectConfig {
    
    private PotionEffectType effectType;  // 药水效果类型
    private int level;                    // 效果等级（0-based）
    private int duration;                 // 持续时间（tick，-1表示永久）
    private boolean ambient;              // 是否为环境效果
    private boolean particles;            // 是否显示粒子
    private boolean icon;                 // 是否显示图标
    
    /**
     * 默认构造函数
     */
    public PotionEffectConfig() {
        this.level = 0;
        this.duration = 600; // 30秒
        this.ambient = false;
        this.particles = true;
        this.icon = true;
    }
    
    /**
     * 构造函数
     * 
     * @param effectType 药水效果类型
     * @param level 效果等级
     * @param duration 持续时间
     */
    public PotionEffectConfig(PotionEffectType effectType, int level, int duration) {
        this();
        this.effectType = effectType;
        this.level = level;
        this.duration = duration;
    }
    
    /**
     * 转换为Bukkit的PotionEffect
     * 
     * @return PotionEffect对象
     */
    public PotionEffect toPotionEffect() {
        if (effectType == null) {
            return null;
        }
        return new PotionEffect(effectType, duration, level, ambient, particles, icon);
    }
    
    // Getter和Setter方法
    public PotionEffectType getEffectType() { return effectType; }
    public void setEffectType(PotionEffectType effectType) { this.effectType = effectType; }
    
    public int getLevel() { return level; }
    public void setLevel(int level) { this.level = level; }
    
    public int getDuration() { return duration; }
    public void setDuration(int duration) { this.duration = duration; }
    
    public boolean isAmbient() { return ambient; }
    public void setAmbient(boolean ambient) { this.ambient = ambient; }
    
    public boolean isParticles() { return particles; }
    public void setParticles(boolean particles) { this.particles = particles; }
    
    public boolean isIcon() { return icon; }
    public void setIcon(boolean icon) { this.icon = icon; }
    
    @Override
    public String toString() {
        return String.format("PotionEffectConfig{type=%s, level=%d, duration=%d}", 
                           effectType, level, duration);
    }
}
