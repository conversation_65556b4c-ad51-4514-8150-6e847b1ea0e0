package org.Ver_zhzh.customZombie.UserMaker;

import org.bukkit.Location;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;
import org.bukkit.plugin.Plugin;

import java.util.logging.Logger;

/**
 * IDZ系统测试类
 * 用于测试IDZ独立怪物系列的各项功能
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class IDZSystemTest {
    
    private final Plugin plugin;
    private final Logger logger;
    private final IDZSystemManager systemManager;
    
    /**
     * 构造函数
     * 
     * @param plugin 插件实例
     * @param systemManager IDZ系统管理器
     */
    public IDZSystemTest(Plugin plugin, IDZSystemManager systemManager) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.systemManager = systemManager;
    }
    
    /**
     * 运行完整的系统测试
     * 
     * @param testLocation 测试位置
     * @return 测试结果
     */
    public boolean runFullTest(Location testLocation) {
        logger.info("========== IDZ系统完整测试开始 ==========");
        
        boolean allTestsPassed = true;
        
        try {
            // 测试1: 系统初始化检查
            logger.info("测试1: 系统初始化检查");
            if (!testSystemInitialization()) {
                logger.severe("测试1失败: 系统初始化检查");
                allTestsPassed = false;
            } else {
                logger.info("测试1通过: 系统初始化正常");
            }
            
            // 测试2: 配置管理测试
            logger.info("测试2: 配置管理测试");
            if (!testConfigurationManagement()) {
                logger.severe("测试2失败: 配置管理测试");
                allTestsPassed = false;
            } else {
                logger.info("测试2通过: 配置管理正常");
            }
            
            // 测试3: 怪物创建测试
            logger.info("测试3: 怪物创建测试");
            CustomMonster testMonster = testMonsterCreation();
            if (testMonster == null) {
                logger.severe("测试3失败: 怪物创建测试");
                allTestsPassed = false;
            } else {
                logger.info("测试3通过: 怪物创建正常");
            }
            
            // 测试4: 实体生成测试
            if (testLocation != null && testMonster != null) {
                logger.info("测试4: 实体生成测试");
                if (!testEntitySpawning(testLocation, testMonster)) {
                    logger.severe("测试4失败: 实体生成测试");
                    allTestsPassed = false;
                } else {
                    logger.info("测试4通过: 实体生成正常");
                }
            } else {
                logger.warning("测试4跳过: 缺少测试位置或测试怪物");
            }
            
            // 测试5: 技能模板测试
            logger.info("测试5: 技能模板测试");
            if (!testSkillTemplates()) {
                logger.severe("测试5失败: 技能模板测试");
                allTestsPassed = false;
            } else {
                logger.info("测试5通过: 技能模板正常");
            }
            
            // 测试6: 元数据标记测试
            if (testLocation != null && testMonster != null) {
                logger.info("测试6: 元数据标记测试");
                if (!testMetadataTags(testLocation, testMonster)) {
                    logger.severe("测试6失败: 元数据标记测试");
                    allTestsPassed = false;
                } else {
                    logger.info("测试6通过: 元数据标记正常");
                }
            } else {
                logger.warning("测试6跳过: 缺少测试位置或测试怪物");
            }
            
        } catch (Exception e) {
            logger.severe("测试过程中发生异常: " + e.getMessage());
            e.printStackTrace();
            allTestsPassed = false;
        }
        
        // 输出测试结果
        if (allTestsPassed) {
            logger.info("========== IDZ系统测试全部通过 ==========");
        } else {
            logger.severe("========== IDZ系统测试存在失败项 ==========");
        }
        
        return allTestsPassed;
    }
    
    /**
     * 测试系统初始化
     */
    private boolean testSystemInitialization() {
        try {
            if (!systemManager.isInitialized()) {
                logger.severe("系统未初始化");
                return false;
            }
            
            if (!systemManager.isEnabled()) {
                logger.severe("系统未启用");
                return false;
            }
            
            if (systemManager.getConfigManager() == null) {
                logger.severe("配置管理器未初始化");
                return false;
            }
            
            if (systemManager.getEntityManager() == null) {
                logger.severe("实体管理器未初始化");
                return false;
            }
            
            if (systemManager.getSkillManager() == null) {
                logger.severe("技能管理器未初始化");
                return false;
            }
            
            logger.info("系统状态: " + systemManager.getSystemStatus());
            return true;
            
        } catch (Exception e) {
            logger.severe("系统初始化测试异常: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 测试配置管理
     */
    private boolean testConfigurationManagement() {
        try {
            CustomMonsterConfigManager configManager = systemManager.getConfigManager();
            
            // 测试配置文件加载
            var allMonsters = configManager.getAllCustomMonsters();
            logger.info("已加载怪物数量: " + allMonsters.size());
            
            // 测试设置获取
            Object maxMonsters = configManager.getSetting("max_custom_monsters", 100);
            logger.info("最大怪物数量设置: " + maxMonsters);
            
            return true;
            
        } catch (Exception e) {
            logger.severe("配置管理测试异常: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 测试怪物创建
     */
    private CustomMonster testMonsterCreation() {
        try {
            // 创建测试怪物
            CustomMonster testMonster = new CustomMonster("idz999", "测试怪物", EntityType.ZOMBIE);
            testMonster.setCreator("IDZ-Test");
            testMonster.setDisplayName("§e[测试] IDZ怪物");
            testMonster.setDescription("这是一个IDZ系统测试怪物");
            testMonster.setHealth(100.0);
            testMonster.setDamage(10.0);
            
            // 添加测试技能
            testMonster.addInheritedSkill("id5_poison_attack");
            testMonster.setSkillParameter("poison_level", 1);
            testMonster.setSkillParameter("poison_duration", 3000);
            
            // 验证怪物配置
            ValidationResult validation = testMonster.validate();
            if (!validation.isValid()) {
                logger.severe("测试怪物配置验证失败: " + validation.getFormattedErrors());
                return null;
            }
            
            logger.info("测试怪物创建成功: " + testMonster.toString());
            return testMonster;
            
        } catch (Exception e) {
            logger.severe("怪物创建测试异常: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 测试实体生成
     */
    private boolean testEntitySpawning(Location location, CustomMonster monster) {
        try {
            IDZEntityManager entityManager = systemManager.getEntityManager();
            
            // 生成测试实体
            boolean spawnResult = entityManager.spawnCustomMonster(location, monster);
            if (!spawnResult) {
                logger.severe("实体生成失败");
                return false;
            }
            
            logger.info("实体生成成功，当前活跃怪物数量: " + entityManager.getActiveMonsterCount());
            return true;
            
        } catch (Exception e) {
            logger.severe("实体生成测试异常: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 测试技能模板
     */
    private boolean testSkillTemplates() {
        try {
            SkillTemplateManager skillManager = systemManager.getSkillManager();
            
            // 测试技能模板数量
            var allTemplates = skillManager.getAllSkillTemplates();
            logger.info("技能模板总数: " + allTemplates.size());
            
            // 测试按来源分组
            var templatesBySource = skillManager.getSkillTemplatesBySource();
            for (var entry : templatesBySource.entrySet()) {
                logger.info("来源 " + entry.getKey() + ": " + entry.getValue().size() + " 个技能");
            }
            
            // 测试特定技能模板
            var poisonTemplate = skillManager.getSkillTemplate("id5_poison_attack");
            if (poisonTemplate == null) {
                logger.severe("未找到剧毒攻击技能模板");
                return false;
            }
            
            logger.info("剧毒攻击技能: " + poisonTemplate.getName() + " - " + poisonTemplate.getDescription());
            return true;
            
        } catch (Exception e) {
            logger.severe("技能模板测试异常: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 测试元数据标记
     */
    private boolean testMetadataTags(Location location, CustomMonster monster) {
        try {
            IDZEntityManager entityManager = systemManager.getEntityManager();
            
            // 生成测试实体
            if (!entityManager.spawnCustomMonster(location, monster)) {
                logger.severe("无法生成测试实体");
                return false;
            }
            
            // 查找生成的实体
            for (LivingEntity entity : location.getWorld().getLivingEntities()) {
                if (entityManager.isIDZCustomMonster(entity)) {
                    // 测试元数据标记
                    String monsterId = entityManager.getIDZMonsterId(entity);
                    String monsterName = entityManager.getIDZMonsterName(entity);
                    
                    if (!monster.getId().equals(monsterId)) {
                        logger.severe("怪物ID不匹配: 期望=" + monster.getId() + ", 实际=" + monsterId);
                        return false;
                    }
                    
                    if (!monster.getName().equals(monsterName)) {
                        logger.severe("怪物名称不匹配: 期望=" + monster.getName() + ", 实际=" + monsterName);
                        return false;
                    }
                    
                    // 检查IDZ系列标记
                    if (!entity.hasMetadata("monsterSeries")) {
                        logger.severe("缺少怪物系列标记");
                        return false;
                    }
                    
                    String series = entity.getMetadata("monsterSeries").get(0).asString();
                    if (!"IDZ".equals(series)) {
                        logger.severe("怪物系列标记错误: 期望=IDZ, 实际=" + series);
                        return false;
                    }
                    
                    logger.info("元数据标记验证通过: ID=" + monsterId + ", 名称=" + monsterName + ", 系列=" + series);
                    
                    // 清理测试实体
                    entity.remove();
                    return true;
                }
            }
            
            logger.severe("未找到生成的IDZ测试实体");
            return false;
            
        } catch (Exception e) {
            logger.severe("元数据标记测试异常: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 快速测试（仅测试基础功能）
     * 
     * @return 测试结果
     */
    public boolean runQuickTest() {
        logger.info("========== IDZ系统快速测试 ==========");
        
        boolean result = testSystemInitialization() && 
                        testConfigurationManagement() && 
                        testSkillTemplates();
        
        if (result) {
            logger.info("IDZ系统快速测试通过");
        } else {
            logger.severe("IDZ系统快速测试失败");
        }
        
        return result;
    }
    
    /**
     * 获取测试报告
     * 
     * @return 测试报告字符串
     */
    public String getTestReport() {
        StringBuilder report = new StringBuilder();
        report.append("IDZ系统测试报告:\n");
        report.append("- 系统状态: ").append(systemManager.isInitialized() ? "已初始化" : "未初始化").append("\n");
        report.append("- 系统版本: ").append(systemManager.getVersionInfo()).append("\n");
        report.append("- ").append(systemManager.getSystemStatus());
        
        return report.toString();
    }
}
