package org.Ver_zhzh.customZombie.UserMaker;

import org.bukkit.command.CommandSender;
import org.bukkit.entity.LivingEntity;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDeathEvent;
import org.bukkit.plugin.Plugin;
import org.bukkit.scheduler.BukkitRunnable;

import java.util.List;
import java.util.logging.Logger;

/**
 * IDZ系统管理器
 * 整合和管理所有IDZ相关组件
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class IDZSystemManager implements Listener {
    
    private final Plugin plugin;
    private final Logger logger;
    
    // 核心组件
    private CustomMonsterConfigManager configManager;
    private SkillTemplateManager skillManager;
    private IDZEntityManager entityManager;
    private IDZCommandHandler commandHandler;
    private IDZEventListener eventListener;
    
    // 系统状态
    private boolean initialized = false;
    private boolean enabled = true;
    
    // 统计信息
    private long systemStartTime;
    
    /**
     * 构造函数
     * 
     * @param plugin 插件实例
     */
    public IDZSystemManager(Plugin plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.systemStartTime = System.currentTimeMillis();
    }
    
    /**
     * 初始化IDZ系统
     * 
     * @return 是否初始化成功
     */
    public boolean initialize() {
        if (initialized) {
            logger.warning("IDZ系统已经初始化，跳过重复初始化");
            return true;
        }
        
        try {
            logger.info("开始初始化IDZ自定义怪物系统...");
            
            // 初始化配置管理器
            logger.info("初始化配置管理器...");
            configManager = new CustomMonsterConfigManager(plugin);
            
            // 初始化技能模板管理器
            logger.info("初始化技能模板管理器...");
            skillManager = new SkillTemplateManager(plugin);
            
            // 初始化实体管理器
            logger.info("初始化实体管理器...");
            entityManager = new IDZEntityManager(plugin, skillManager);
            
            // 初始化命令处理器
            logger.info("初始化命令处理器...");
            commandHandler = new IDZCommandHandler(plugin, configManager, entityManager, this);

            // 初始化事件监听器
            logger.info("初始化事件监听器...");
            boolean debugMode = plugin.getConfig().getBoolean("debug", false);
            eventListener = new IDZEventListener(plugin, entityManager, debugMode);

            // 注册事件监听器
            plugin.getServer().getPluginManager().registerEvents(this, plugin);
            plugin.getServer().getPluginManager().registerEvents(eventListener, plugin);
            
            // 启动定期清理任务
            startCleanupTask();
            
            // 启动自动保存任务
            startAutoSaveTask();
            
            initialized = true;
            logger.info("IDZ自定义怪物系统初始化完成！");
            
            // 输出系统信息
            logSystemInfo();
            
            return true;
            
        } catch (Exception e) {
            logger.severe("IDZ系统初始化失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 关闭IDZ系统
     */
    public void shutdown() {
        if (!initialized) {
            return;
        }
        
        try {
            logger.info("正在关闭IDZ自定义怪物系统...");
            
            // 保存配置
            if (configManager != null) {
                configManager.saveConfig();
            }
            
            // 清理活跃实体
            if (entityManager != null) {
                entityManager.cleanupInvalidEntities();
            }
            
            // 禁用所有技能
            if (skillManager != null) {
                // 清理技能相关资源
            }
            
            initialized = false;
            enabled = false;
            
            logger.info("IDZ系统已关闭");
            
        } catch (Exception e) {
            logger.severe("关闭IDZ系统时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 处理IDZ命令
     * 
     * @param sender 命令发送者
     * @param args 命令参数
     * @return 是否处理成功
     */
    public boolean handleCommand(CommandSender sender, String[] args) {
        if (!initialized || !enabled) {
            sender.sendMessage("§cIDZ系统未初始化或已禁用");
            return true;
        }
        
        if (commandHandler == null) {
            sender.sendMessage("§c命令处理器未初始化");
            return true;
        }
        
        return commandHandler.handleIDZCommand(sender, args);
    }
    
    /**
     * 获取命令补全建议
     * 
     * @param args 命令参数
     * @return 补全建议列表
     */
    public List<String> getTabCompletions(String[] args) {
        if (!initialized || !enabled || commandHandler == null) {
            return null;
        }
        
        return commandHandler.getTabCompletions(args);
    }
    
    /**
     * 处理实体死亡事件
     */
    @EventHandler
    public void onEntityDeath(EntityDeathEvent event) {
        if (!initialized || !enabled) {
            return;
        }
        
        LivingEntity entity = event.getEntity();
        
        // 检查是否为IDZ自定义怪物
        if (entityManager.isIDZCustomMonster(entity)) {
            entityManager.handleMonsterDeath(entity);
            
            // 禁用技能
            if (skillManager != null) {
                skillManager.disableAllSkills(entity);
            }
        }
    }
    
    /**
     * 启动定期清理任务
     */
    private void startCleanupTask() {
        new BukkitRunnable() {
            @Override
            public void run() {
                if (!initialized || !enabled) {
                    this.cancel();
                    return;
                }
                
                try {
                    // 清理无效的实体引用
                    if (entityManager != null) {
                        entityManager.cleanupInvalidEntities();
                    }
                } catch (Exception e) {
                    logger.warning("定期清理任务执行失败: " + e.getMessage());
                }
            }
        }.runTaskTimer(plugin, 20 * 60, 20 * 60); // 每分钟执行一次
    }
    
    /**
     * 启动自动保存任务
     */
    private void startAutoSaveTask() {
        new BukkitRunnable() {
            @Override
            public void run() {
                if (!initialized || !enabled) {
                    this.cancel();
                    return;
                }
                
                try {
                    // 自动保存配置
                    if (configManager != null) {
                        configManager.saveConfig();
                        logger.info("IDZ配置自动保存完成");
                    }
                } catch (Exception e) {
                    logger.warning("自动保存任务执行失败: " + e.getMessage());
                }
            }
        }.runTaskTimer(plugin, 20 * 300, 20 * 300); // 每5分钟执行一次
    }
    
    /**
     * 输出系统信息
     */
    private void logSystemInfo() {
        logger.info("========== IDZ独立怪物系列 ==========");
        logger.info("系列名称: IDZ (Independent DeathZombie)");
        logger.info("系统版本: 1.0");
        logger.info("初始化时间: " + new java.util.Date(systemStartTime));
        logger.info("系列特点: 完全独立的自定义怪物系列");

        if (configManager != null) {
            int monsterCount = configManager.getAllCustomMonsters().size();
            int enabledCount = configManager.getEnabledCustomMonsters().size();
            logger.info("IDZ怪物: " + monsterCount + " 个 (启用: " + enabledCount + " 个)");
        }

        if (skillManager != null) {
            // 使用技能统计工具
            SkillStatistics skillStats = new SkillStatistics(skillManager, logger);
            skillStats.logStatistics();

            // 验证技能数量
            if (!skillStats.validateSkillCounts()) {
                logger.warning("技能数量验证失败，请检查技能模板注册");
            }
        }

        if (entityManager != null) {
            logger.info("实体统计: " + entityManager.getStatistics());
        }

        if (eventListener != null) {
            logger.info("事件监听: " + eventListener.getStatistics());
        }

        logger.info("系统状态: " + (enabled ? "启用" : "禁用"));
        logger.info("元数据标记: idzCustomMonster, monsterSeries=IDZ");
        logger.info("=====================================");
    }
    
    /**
     * 重新加载系统
     * 
     * @return 是否重新加载成功
     */
    public boolean reload() {
        try {
            logger.info("正在重新加载IDZ系统...");
            
            // 重新加载配置
            if (configManager != null) {
                configManager.reloadConfig();
            }
            
            // 清理无效实体
            if (entityManager != null) {
                entityManager.cleanupInvalidEntities();
            }
            
            logger.info("IDZ系统重新加载完成");
            logSystemInfo();
            
            return true;
            
        } catch (Exception e) {
            logger.severe("重新加载IDZ系统失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 启用系统
     */
    public void enable() {
        enabled = true;
        logger.info("IDZ系统已启用");
    }
    
    /**
     * 禁用系统
     */
    public void disable() {
        enabled = false;
        logger.info("IDZ系统已禁用");
    }
    
    /**
     * 获取系统状态信息
     * 
     * @return 状态信息字符串
     */
    public String getSystemStatus() {
        StringBuilder status = new StringBuilder();
        status.append("IDZ系统状态:\n");
        status.append("- 初始化: ").append(initialized ? "是" : "否").append("\n");
        status.append("- 启用: ").append(enabled ? "是" : "否").append("\n");
        status.append("- 运行时间: ").append(getUptime()).append("\n");
        
        if (configManager != null) {
            int total = configManager.getAllCustomMonsters().size();
            int enabled = configManager.getEnabledCustomMonsters().size();
            status.append("- 自定义怪物: ").append(total).append(" 个 (启用: ").append(enabled).append(" 个)\n");
        }
        
        if (skillManager != null) {
            status.append("- 技能模板: ").append(skillManager.getAllSkillTemplates().size()).append(" 个\n");
        }
        
        if (entityManager != null) {
            status.append("- ").append(entityManager.getStatistics()).append("\n");
        }
        
        return status.toString();
    }
    
    /**
     * 获取系统运行时间
     * 
     * @return 运行时间字符串
     */
    private String getUptime() {
        long uptime = System.currentTimeMillis() - systemStartTime;
        long seconds = uptime / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        long days = hours / 24;
        
        if (days > 0) {
            return String.format("%d天 %d小时 %d分钟", days, hours % 24, minutes % 60);
        } else if (hours > 0) {
            return String.format("%d小时 %d分钟", hours, minutes % 60);
        } else if (minutes > 0) {
            return String.format("%d分钟 %d秒", minutes, seconds % 60);
        } else {
            return String.format("%d秒", seconds);
        }
    }
    
    // Getter方法
    public CustomMonsterConfigManager getConfigManager() { return configManager; }
    public SkillTemplateManager getSkillManager() { return skillManager; }
    public IDZEntityManager getEntityManager() { return entityManager; }
    public IDZCommandHandler getCommandHandler() { return commandHandler; }
    
    public boolean isInitialized() { return initialized; }
    public boolean isEnabled() { return enabled; }
    
    /**
     * 获取系统版本信息
     *
     * @return 版本信息
     */
    public String getVersionInfo() {
        return "IDZ独立怪物系列 v1.0 - Independent DeathZombie Series";
    }

    /**
     * 运行系统测试
     *
     * @param testLocation 测试位置（可选）
     * @return 测试结果
     */
    public boolean runSystemTest(org.bukkit.Location testLocation) {
        if (!initialized) {
            logger.warning("系统未初始化，无法运行测试");
            return false;
        }

        try {
            IDZSystemTest tester = new IDZSystemTest(plugin, this);

            if (testLocation != null) {
                return tester.runFullTest(testLocation);
            } else {
                return tester.runQuickTest();
            }

        } catch (Exception e) {
            logger.severe("运行系统测试失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 获取系统测试报告
     *
     * @return 测试报告
     */
    public String getSystemTestReport() {
        if (!initialized) {
            return "IDZ系统未初始化，无法生成测试报告";
        }

        try {
            IDZSystemTest tester = new IDZSystemTest(plugin, this);
            return tester.getTestReport();
        } catch (Exception e) {
            return "生成测试报告失败: " + e.getMessage();
        }
    }
}
