package org.Ver_zhzh.customZombie.UserMaker;

import org.bukkit.Material;
import org.bukkit.inventory.ItemStack;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 奖励配置类
 * 用于配置击杀自定义怪物后的奖励
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class RewardConfig {
    
    // 金钱奖励
    private double money;                 // 金钱数量
    private double moneyMin;              // 最小金钱数量
    private double moneyMax;              // 最大金钱数量
    private boolean randomMoney;          // 是否随机金钱
    
    // 经验奖励
    private int experience;               // 经验值
    private int experienceMin;            // 最小经验值
    private int experienceMax;            // 最大经验值
    private boolean randomExperience;     // 是否随机经验
    
    // 物品奖励
    private List<ItemReward> itemRewards; // 物品奖励列表
    
    // 命令奖励
    private List<CommandReward> commandRewards; // 命令奖励列表
    
    // 特殊奖励
    private Map<String, Object> specialRewards; // 特殊奖励
    
    /**
     * 物品奖励内部类
     */
    public static class ItemReward {
        private Material material;        // 物品材料
        private int amount;               // 数量
        private int amountMin;            // 最小数量
        private int amountMax;            // 最大数量
        private boolean randomAmount;     // 是否随机数量
        private double dropChance;        // 掉落概率（0.0-1.0）
        private String displayName;       // 显示名称
        private List<String> lore;        // 物品描述
        private Map<String, Integer> enchantments; // 附魔
        
        public ItemReward() {
            this.amount = 1;
            this.dropChance = 1.0;
            this.lore = new ArrayList<>();
            this.enchantments = new HashMap<>();
        }
        
        public ItemReward(Material material, int amount, double dropChance) {
            this();
            this.material = material;
            this.amount = amount;
            this.dropChance = dropChance;
        }
        
        /**
         * 转换为ItemStack
         * 
         * @return ItemStack对象
         */
        public ItemStack toItemStack() {
            if (material == null) {
                return null;
            }
            
            int finalAmount = amount;
            if (randomAmount && amountMin > 0 && amountMax > amountMin) {
                finalAmount = amountMin + (int) (Math.random() * (amountMax - amountMin + 1));
            }
            
            ItemStack item = new ItemStack(material, finalAmount);
            
            // 设置显示名称和描述
            if (displayName != null || !lore.isEmpty()) {
                var meta = item.getItemMeta();
                if (meta != null) {
                    if (displayName != null) {
                        meta.setDisplayName(displayName);
                    }
                    if (!lore.isEmpty()) {
                        meta.setLore(lore);
                    }
                    item.setItemMeta(meta);
                }
            }
            
            // 添加附魔
            for (Map.Entry<String, Integer> entry : enchantments.entrySet()) {
                try {
                    var enchantment = org.bukkit.enchantments.Enchantment.getByName(entry.getKey());
                    if (enchantment != null) {
                        item.addUnsafeEnchantment(enchantment, entry.getValue());
                    }
                } catch (Exception e) {
                    // 忽略无效的附魔
                }
            }
            
            return item;
        }
        
        /**
         * 检查是否应该掉落
         * 
         * @return 是否掉落
         */
        public boolean shouldDrop() {
            return Math.random() < dropChance;
        }
        
        // Getter和Setter方法
        public Material getMaterial() { return material; }
        public void setMaterial(Material material) { this.material = material; }
        
        public int getAmount() { return amount; }
        public void setAmount(int amount) { this.amount = amount; }
        
        public int getAmountMin() { return amountMin; }
        public void setAmountMin(int amountMin) { this.amountMin = amountMin; }
        
        public int getAmountMax() { return amountMax; }
        public void setAmountMax(int amountMax) { this.amountMax = amountMax; }
        
        public boolean isRandomAmount() { return randomAmount; }
        public void setRandomAmount(boolean randomAmount) { this.randomAmount = randomAmount; }
        
        public double getDropChance() { return dropChance; }
        public void setDropChance(double dropChance) { this.dropChance = dropChance; }
        
        public String getDisplayName() { return displayName; }
        public void setDisplayName(String displayName) { this.displayName = displayName; }
        
        public List<String> getLore() { return lore; }
        public void setLore(List<String> lore) { this.lore = lore; }
        
        public Map<String, Integer> getEnchantments() { return enchantments; }
        public void setEnchantments(Map<String, Integer> enchantments) { this.enchantments = enchantments; }
    }
    
    /**
     * 命令奖励内部类
     */
    public static class CommandReward {
        private String command;           // 命令内容
        private double executeChance;     // 执行概率（0.0-1.0）
        private boolean asConsole;        // 是否以控制台身份执行
        private String description;       // 命令描述
        
        public CommandReward() {
            this.executeChance = 1.0;
            this.asConsole = true;
        }
        
        public CommandReward(String command, double executeChance, boolean asConsole) {
            this();
            this.command = command;
            this.executeChance = executeChance;
            this.asConsole = asConsole;
        }
        
        /**
         * 检查是否应该执行
         * 
         * @return 是否执行
         */
        public boolean shouldExecute() {
            return Math.random() < executeChance;
        }
        
        // Getter和Setter方法
        public String getCommand() { return command; }
        public void setCommand(String command) { this.command = command; }
        
        public double getExecuteChance() { return executeChance; }
        public void setExecuteChance(double executeChance) { this.executeChance = executeChance; }
        
        public boolean isAsConsole() { return asConsole; }
        public void setAsConsole(boolean asConsole) { this.asConsole = asConsole; }
        
        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }
    }
    
    /**
     * 默认构造函数
     */
    public RewardConfig() {
        this.money = 0.0;
        this.experience = 0;
        this.itemRewards = new ArrayList<>();
        this.commandRewards = new ArrayList<>();
        this.specialRewards = new HashMap<>();
    }
    
    /**
     * 获取最终金钱奖励
     * 
     * @return 金钱数量
     */
    public double getFinalMoney() {
        if (randomMoney && moneyMin >= 0 && moneyMax > moneyMin) {
            return moneyMin + Math.random() * (moneyMax - moneyMin);
        }
        return money;
    }
    
    /**
     * 获取最终经验奖励
     * 
     * @return 经验值
     */
    public int getFinalExperience() {
        if (randomExperience && experienceMin >= 0 && experienceMax > experienceMin) {
            return experienceMin + (int) (Math.random() * (experienceMax - experienceMin + 1));
        }
        return experience;
    }
    
    /**
     * 添加物品奖励
     * 
     * @param itemReward 物品奖励
     */
    public void addItemReward(ItemReward itemReward) {
        this.itemRewards.add(itemReward);
    }
    
    /**
     * 添加命令奖励
     * 
     * @param commandReward 命令奖励
     */
    public void addCommandReward(CommandReward commandReward) {
        this.commandRewards.add(commandReward);
    }
    
    /**
     * 设置特殊奖励
     * 
     * @param key 奖励键
     * @param value 奖励值
     */
    public void setSpecialReward(String key, Object value) {
        this.specialRewards.put(key, value);
    }
    
    // Getter和Setter方法
    public double getMoney() { return money; }
    public void setMoney(double money) { this.money = money; }
    
    public double getMoneyMin() { return moneyMin; }
    public void setMoneyMin(double moneyMin) { this.moneyMin = moneyMin; }
    
    public double getMoneyMax() { return moneyMax; }
    public void setMoneyMax(double moneyMax) { this.moneyMax = moneyMax; }
    
    public boolean isRandomMoney() { return randomMoney; }
    public void setRandomMoney(boolean randomMoney) { this.randomMoney = randomMoney; }
    
    public int getExperience() { return experience; }
    public void setExperience(int experience) { this.experience = experience; }
    
    public int getExperienceMin() { return experienceMin; }
    public void setExperienceMin(int experienceMin) { this.experienceMin = experienceMin; }
    
    public int getExperienceMax() { return experienceMax; }
    public void setExperienceMax(int experienceMax) { this.experienceMax = experienceMax; }
    
    public boolean isRandomExperience() { return randomExperience; }
    public void setRandomExperience(boolean randomExperience) { this.randomExperience = randomExperience; }
    
    public List<ItemReward> getItemRewards() { return itemRewards; }
    public void setItemRewards(List<ItemReward> itemRewards) { this.itemRewards = itemRewards; }
    
    public List<CommandReward> getCommandRewards() { return commandRewards; }
    public void setCommandRewards(List<CommandReward> commandRewards) { this.commandRewards = commandRewards; }
    
    public Map<String, Object> getSpecialRewards() { return specialRewards; }
    public void setSpecialRewards(Map<String, Object> specialRewards) { this.specialRewards = specialRewards; }
}
