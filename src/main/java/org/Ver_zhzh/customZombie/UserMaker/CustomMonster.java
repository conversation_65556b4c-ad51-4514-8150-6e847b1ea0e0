package org.Ver_zhzh.customZombie.UserMaker;

import org.bukkit.Material;
import org.bukkit.entity.EntityType;
import org.bukkit.potion.PotionEffectType;

import java.util.*;

/**
 * 自定义怪物数据结构
 * 用于存储用户创建的IDZ系列怪物的所有配置信息
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class CustomMonster {
    
    // 基础信息
    private String id;                    // 怪物ID (idz1, idz2, ...)
    private String name;                  // 怪物名称
    private String displayName;           // 显示名称（支持颜色代码）
    private EntityType entityType;        // 实体类型
    private String description;           // 怪物描述
    
    // 属性配置
    private double health;                // 生命值
    private double damage;                // 攻击力
    private double speed;                 // 移动速度
    private double armor;                 // 护甲值
    private double armorToughness;        // 护甲韧性
    private double knockbackResistance;   // 击退抗性
    
    // 装备配置
    private Material weapon;              // 武器
    private Material helmet;              // 头盔
    private Material chestplate;          // 胸甲
    private Material leggings;            // 护腿
    private Material boots;               // 靴子
    private Map<String, Integer> weaponEnchantments;  // 武器附魔
    private Map<String, Integer> armorEnchantments;   // 护甲附魔
    
    // 技能配置
    private List<String> inheritedSkills; // 继承的技能模板ID
    private Map<String, Object> skillParameters; // 技能参数配置
    
    // 粒子效果配置
    private Map<String, ParticleEffect> particleEffects; // 粒子效果
    
    // 药水效果配置
    private Map<PotionEffectType, PotionEffectConfig> potionEffects; // 药水效果
    
    // 奖励配置
    private RewardConfig rewards;         // 击杀奖励配置
    
    // 元数据
    private String creator;               // 创建者
    private long createTime;              // 创建时间
    private long lastModified;            // 最后修改时间
    private boolean enabled;              // 是否启用
    
    /**
     * 默认构造函数
     */
    public CustomMonster() {
        this.weaponEnchantments = new HashMap<>();
        this.armorEnchantments = new HashMap<>();
        this.inheritedSkills = new ArrayList<>();
        this.skillParameters = new HashMap<>();
        this.particleEffects = new HashMap<>();
        this.potionEffects = new HashMap<>();
        this.enabled = true;
        this.createTime = System.currentTimeMillis();
        this.lastModified = System.currentTimeMillis();
    }
    
    /**
     * 构造函数
     * 
     * @param id 怪物ID
     * @param name 怪物名称
     * @param entityType 实体类型
     */
    public CustomMonster(String id, String name, EntityType entityType) {
        this();
        this.id = id;
        this.name = name;
        this.displayName = name;
        this.entityType = entityType;
        this.health = 100.0;
        this.damage = 10.0;
        this.speed = 0.25;
    }
    
    // Getter和Setter方法
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }
    
    public String getName() { return name; }
    public void setName(String name) { this.name = name; }
    
    public String getDisplayName() { return displayName; }
    public void setDisplayName(String displayName) { this.displayName = displayName; }
    
    public EntityType getEntityType() { return entityType; }
    public void setEntityType(EntityType entityType) { this.entityType = entityType; }
    
    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }
    
    public double getHealth() { return health; }
    public void setHealth(double health) { this.health = health; }
    
    public double getDamage() { return damage; }
    public void setDamage(double damage) { this.damage = damage; }
    
    public double getSpeed() { return speed; }
    public void setSpeed(double speed) { this.speed = speed; }
    
    public double getArmor() { return armor; }
    public void setArmor(double armor) { this.armor = armor; }
    
    public double getArmorToughness() { return armorToughness; }
    public void setArmorToughness(double armorToughness) { this.armorToughness = armorToughness; }
    
    public double getKnockbackResistance() { return knockbackResistance; }
    public void setKnockbackResistance(double knockbackResistance) { this.knockbackResistance = knockbackResistance; }
    
    public Material getWeapon() { return weapon; }
    public void setWeapon(Material weapon) { this.weapon = weapon; }
    
    public Material getHelmet() { return helmet; }
    public void setHelmet(Material helmet) { this.helmet = helmet; }
    
    public Material getChestplate() { return chestplate; }
    public void setChestplate(Material chestplate) { this.chestplate = chestplate; }
    
    public Material getLeggings() { return leggings; }
    public void setLeggings(Material leggings) { this.leggings = leggings; }
    
    public Material getBoots() { return boots; }
    public void setBoots(Material boots) { this.boots = boots; }
    
    public Map<String, Integer> getWeaponEnchantments() { return weaponEnchantments; }
    public void setWeaponEnchantments(Map<String, Integer> weaponEnchantments) { this.weaponEnchantments = weaponEnchantments; }
    
    public Map<String, Integer> getArmorEnchantments() { return armorEnchantments; }
    public void setArmorEnchantments(Map<String, Integer> armorEnchantments) { this.armorEnchantments = armorEnchantments; }
    
    public List<String> getInheritedSkills() { return inheritedSkills; }
    public void setInheritedSkills(List<String> inheritedSkills) { this.inheritedSkills = inheritedSkills; }
    
    public Map<String, Object> getSkillParameters() { return skillParameters; }
    public void setSkillParameters(Map<String, Object> skillParameters) { this.skillParameters = skillParameters; }
    
    public Map<String, ParticleEffect> getParticleEffects() { return particleEffects; }
    public void setParticleEffects(Map<String, ParticleEffect> particleEffects) { this.particleEffects = particleEffects; }
    
    public Map<PotionEffectType, PotionEffectConfig> getPotionEffects() { return potionEffects; }
    public void setPotionEffects(Map<PotionEffectType, PotionEffectConfig> potionEffects) { this.potionEffects = potionEffects; }
    
    public RewardConfig getRewards() { return rewards; }
    public void setRewards(RewardConfig rewards) { this.rewards = rewards; }
    
    public String getCreator() { return creator; }
    public void setCreator(String creator) { this.creator = creator; }
    
    public long getCreateTime() { return createTime; }
    public void setCreateTime(long createTime) { this.createTime = createTime; }
    
    public long getLastModified() { return lastModified; }
    public void setLastModified(long lastModified) { this.lastModified = lastModified; }
    
    public boolean isEnabled() { return enabled; }
    public void setEnabled(boolean enabled) { this.enabled = enabled; }
    
    /**
     * 更新最后修改时间
     */
    public void updateLastModified() {
        this.lastModified = System.currentTimeMillis();
    }
    
    /**
     * 添加继承技能
     * 
     * @param skillId 技能ID
     */
    public void addInheritedSkill(String skillId) {
        if (!this.inheritedSkills.contains(skillId)) {
            this.inheritedSkills.add(skillId);
            updateLastModified();
        }
    }
    
    /**
     * 移除继承技能
     * 
     * @param skillId 技能ID
     */
    public void removeInheritedSkill(String skillId) {
        if (this.inheritedSkills.remove(skillId)) {
            updateLastModified();
        }
    }
    
    /**
     * 设置技能参数
     * 
     * @param key 参数键
     * @param value 参数值
     */
    public void setSkillParameter(String key, Object value) {
        this.skillParameters.put(key, value);
        updateLastModified();
    }
    
    /**
     * 获取技能参数
     * 
     * @param key 参数键
     * @param defaultValue 默认值
     * @return 参数值
     */
    @SuppressWarnings("unchecked")
    public <T> T getSkillParameter(String key, T defaultValue) {
        Object value = this.skillParameters.get(key);
        if (value != null && defaultValue.getClass().isInstance(value)) {
            return (T) value;
        }
        return defaultValue;
    }
    
    /**
     * 添加粒子效果
     * 
     * @param trigger 触发条件
     * @param effect 粒子效果
     */
    public void addParticleEffect(String trigger, ParticleEffect effect) {
        this.particleEffects.put(trigger, effect);
        updateLastModified();
    }
    
    /**
     * 验证怪物配置是否有效
     * 
     * @return 验证结果
     */
    public ValidationResult validate() {
        ValidationResult result = new ValidationResult();
        
        // 验证基础信息
        if (id == null || id.trim().isEmpty()) {
            result.addError("怪物ID不能为空");
        }
        if (name == null || name.trim().isEmpty()) {
            result.addError("怪物名称不能为空");
        }
        if (entityType == null) {
            result.addError("实体类型不能为空");
        }
        
        // 验证属性值
        if (health <= 0) {
            result.addError("生命值必须大于0");
        }
        if (damage < 0) {
            result.addError("攻击力不能为负数");
        }
        if (speed < 0) {
            result.addError("移动速度不能为负数");
        }
        
        return result;
    }
    
    @Override
    public String toString() {
        return String.format("CustomMonster{id='%s', name='%s', entityType=%s, health=%.1f, damage=%.1f}", 
                           id, name, entityType, health, damage);
    }
}
