package org.Ver_zhzh.customZombie.UserMaker;

import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.EntityType;
import org.bukkit.plugin.Plugin;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.logging.Logger;

/**
 * 自定义怪物配置管理器
 * 负责管理Custom.yml配置文件的读写操作
 * 
 * <AUTHOR>
 * @version 1.0
 */
public class CustomMonsterConfigManager {
    
    private final Plugin plugin;
    private final Logger logger;
    private final File configFile;
    private FileConfiguration config;
    
    // 缓存的自定义怪物
    private final Map<String, CustomMonster> customMonsters;
    
    // 配置文件路径
    private static final String CONFIG_FILE_NAME = "Custom.yml";
    
    // 配置节点路径
    private static final String MONSTERS_PATH = "custom_monsters";
    private static final String SETTINGS_PATH = "settings";
    
    /**
     * 构造函数
     * 
     * @param plugin 插件实例
     */
    public CustomMonsterConfigManager(Plugin plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.configFile = new File(plugin.getDataFolder(), CONFIG_FILE_NAME);
        this.customMonsters = new HashMap<>();
        
        // 初始化配置文件
        initializeConfig();
        
        // 加载自定义怪物
        loadCustomMonsters();
    }
    
    /**
     * 初始化配置文件
     */
    private void initializeConfig() {
        try {
            // 确保数据文件夹存在
            if (!plugin.getDataFolder().exists()) {
                plugin.getDataFolder().mkdirs();
            }
            
            // 如果配置文件不存在，创建默认配置
            if (!configFile.exists()) {
                createDefaultConfig();
            }
            
            // 加载配置文件
            config = YamlConfiguration.loadConfiguration(configFile);
            
            logger.info("Custom.yml配置文件初始化完成");
            
        } catch (Exception e) {
            logger.severe("初始化Custom.yml配置文件失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 创建默认配置文件
     */
    private void createDefaultConfig() {
        try {
            configFile.createNewFile();
            
            FileConfiguration defaultConfig = YamlConfiguration.loadConfiguration(configFile);
            
            // 设置默认配置
            defaultConfig.set(SETTINGS_PATH + ".max_custom_monsters", 100);
            defaultConfig.set(SETTINGS_PATH + ".enable_particle_effects", true);
            defaultConfig.set(SETTINGS_PATH + ".debug_mode", false);
            defaultConfig.set(SETTINGS_PATH + ".auto_save_interval", 300); // 5分钟
            
            // 添加示例怪物
            createExampleMonster(defaultConfig);
            
            // 保存默认配置
            defaultConfig.save(configFile);
            
            logger.info("已创建默认的Custom.yml配置文件");
            
        } catch (IOException e) {
            logger.severe("创建默认Custom.yml配置文件失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 创建示例怪物配置
     */
    private void createExampleMonster(FileConfiguration config) {
        String examplePath = MONSTERS_PATH + ".IDZ示例怪物";

        config.set(examplePath + ".id", "idz1");
        config.set(examplePath + ".display_name", "§6[IDZ] §c示例超级僵尸");
        config.set(examplePath + ".entity_type", "ZOMBIE");
        config.set(examplePath + ".description", "IDZ系列示例怪物 - 独立自定义怪物系列");
        config.set(examplePath + ".health", 200.0);
        config.set(examplePath + ".damage", 15.0);
        config.set(examplePath + ".speed", 0.3);
        config.set(examplePath + ".enabled", false); // 默认禁用示例

        // 继承技能示例（展示ID+IDC技能组合）
        List<String> skills = Arrays.asList("id5_poison_attack", "idc3_flame_attack", "id13_lightning_attack");
        config.set(examplePath + ".inherited_skills", skills);

        // 技能参数示例
        config.set(examplePath + ".skill_parameters.poison_level", 2);
        config.set(examplePath + ".skill_parameters.poison_duration", 5000);
        config.set(examplePath + ".skill_parameters.flame_damage", 8.0);
        config.set(examplePath + ".skill_parameters.lightning_damage", 10.0);

        // 装备示例
        config.set(examplePath + ".equipment.weapon", "DIAMOND_SWORD");
        config.set(examplePath + ".equipment.helmet", "DIAMOND_HELMET");
        config.set(examplePath + ".equipment.enchantments.SHARPNESS", 3);
        config.set(examplePath + ".equipment.enchantments.PROTECTION", 2);

        // 粒子效果示例（展示多种效果）
        config.set(examplePath + ".particle_effects.spawn.type", "FLAME");
        config.set(examplePath + ".particle_effects.spawn.count", 50);
        config.set(examplePath + ".particle_effects.spawn.spread", 2.0);
        config.set(examplePath + ".particle_effects.spawn.duration", 3000);

        config.set(examplePath + ".particle_effects.attack.type", "CRIT");
        config.set(examplePath + ".particle_effects.attack.count", 20);
        config.set(examplePath + ".particle_effects.attack.spread", 1.0);

        config.set(examplePath + ".particle_effects.death.type", "EXPLOSION_LARGE");
        config.set(examplePath + ".particle_effects.death.count", 30);
        config.set(examplePath + ".particle_effects.death.spread", 3.0);

        // 奖励示例
        config.set(examplePath + ".rewards.money", 150.0);
        config.set(examplePath + ".rewards.experience", 75);
        config.set(examplePath + ".rewards.random_money", true);
        config.set(examplePath + ".rewards.money_min", 100.0);
        config.set(examplePath + ".rewards.money_max", 200.0);

        // 元数据
        config.set(examplePath + ".creator", "IDZ-System");
        config.set(examplePath + ".create_time", System.currentTimeMillis());

        // 添加说明注释（通过配置值的方式）
        config.set(examplePath + "._description", Arrays.asList(
            "IDZ (Independent DeathZombie) 系列示例怪物",
            "特点: 独立的自定义怪物系列，支持继承ID+IDC技能",
            "元数据标记: idzCustomMonster, monsterSeries=IDZ",
            "可继承85个技能模板，支持粒子效果和奖励系统"
        ));
    }
    
    /**
     * 加载所有自定义怪物
     */
    public void loadCustomMonsters() {
        customMonsters.clear();
        
        ConfigurationSection monstersSection = config.getConfigurationSection(MONSTERS_PATH);
        if (monstersSection == null) {
            logger.info("未找到自定义怪物配置");
            return;
        }
        
        int loadedCount = 0;
        int errorCount = 0;
        
        for (String monsterName : monstersSection.getKeys(false)) {
            try {
                CustomMonster monster = loadMonsterFromConfig(monsterName, monstersSection.getConfigurationSection(monsterName));
                if (monster != null) {
                    customMonsters.put(monsterName, monster);
                    loadedCount++;
                } else {
                    errorCount++;
                }
            } catch (Exception e) {
                logger.warning("加载自定义怪物 '" + monsterName + "' 失败: " + e.getMessage());
                errorCount++;
            }
        }
        
        logger.info(String.format("自定义怪物加载完成: 成功 %d 个, 失败 %d 个", loadedCount, errorCount));
    }
    
    /**
     * 从配置节点加载怪物
     */
    private CustomMonster loadMonsterFromConfig(String name, ConfigurationSection section) {
        if (section == null) {
            return null;
        }
        
        try {
            CustomMonster monster = new CustomMonster();
            
            // 基础信息
            monster.setId(section.getString("id", "idz1"));
            monster.setName(name);
            monster.setDisplayName(section.getString("display_name", name));
            monster.setDescription(section.getString("description", ""));
            
            // 实体类型
            String entityTypeStr = section.getString("entity_type", "ZOMBIE");
            try {
                monster.setEntityType(EntityType.valueOf(entityTypeStr.toUpperCase()));
            } catch (IllegalArgumentException e) {
                logger.warning("无效的实体类型: " + entityTypeStr + ", 使用默认值 ZOMBIE");
                monster.setEntityType(EntityType.ZOMBIE);
            }
            
            // 属性
            monster.setHealth(section.getDouble("health", 100.0));
            monster.setDamage(section.getDouble("damage", 10.0));
            monster.setSpeed(section.getDouble("speed", 0.25));
            monster.setArmor(section.getDouble("armor", 0.0));
            monster.setArmorToughness(section.getDouble("armor_toughness", 0.0));
            monster.setKnockbackResistance(section.getDouble("knockback_resistance", 0.0));
            
            // 继承技能
            List<String> skills = section.getStringList("inherited_skills");
            monster.setInheritedSkills(new ArrayList<>(skills));
            
            // 技能参数
            ConfigurationSection skillParamsSection = section.getConfigurationSection("skill_parameters");
            if (skillParamsSection != null) {
                Map<String, Object> skillParams = new HashMap<>();
                for (String key : skillParamsSection.getKeys(false)) {
                    skillParams.put(key, skillParamsSection.get(key));
                }
                monster.setSkillParameters(skillParams);
            }
            
            // 装备配置
            loadEquipmentConfig(monster, section.getConfigurationSection("equipment"));
            
            // 粒子效果配置
            loadParticleEffectsConfig(monster, section.getConfigurationSection("particle_effects"));
            
            // 奖励配置
            loadRewardsConfig(monster, section.getConfigurationSection("rewards"));
            
            // 元数据
            monster.setCreator(section.getString("creator", "Unknown"));
            monster.setCreateTime(section.getLong("create_time", System.currentTimeMillis()));
            monster.setLastModified(section.getLong("last_modified", System.currentTimeMillis()));
            monster.setEnabled(section.getBoolean("enabled", true));
            
            return monster;
            
        } catch (Exception e) {
            logger.warning("解析怪物配置失败: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * 加载装备配置
     */
    private void loadEquipmentConfig(CustomMonster monster, ConfigurationSection section) {
        if (section == null) return;
        
        try {
            // 武器和护甲
            String weapon = section.getString("weapon");
            if (weapon != null) {
                monster.setWeapon(org.bukkit.Material.valueOf(weapon.toUpperCase()));
            }
            
            String helmet = section.getString("helmet");
            if (helmet != null) {
                monster.setHelmet(org.bukkit.Material.valueOf(helmet.toUpperCase()));
            }
            
            String chestplate = section.getString("chestplate");
            if (chestplate != null) {
                monster.setChestplate(org.bukkit.Material.valueOf(chestplate.toUpperCase()));
            }
            
            String leggings = section.getString("leggings");
            if (leggings != null) {
                monster.setLeggings(org.bukkit.Material.valueOf(leggings.toUpperCase()));
            }
            
            String boots = section.getString("boots");
            if (boots != null) {
                monster.setBoots(org.bukkit.Material.valueOf(boots.toUpperCase()));
            }
            
            // 附魔
            ConfigurationSection enchantSection = section.getConfigurationSection("enchantments");
            if (enchantSection != null) {
                Map<String, Integer> enchantments = new HashMap<>();
                for (String enchant : enchantSection.getKeys(false)) {
                    enchantments.put(enchant, enchantSection.getInt(enchant));
                }
                monster.setWeaponEnchantments(enchantments);
            }
            
        } catch (Exception e) {
            logger.warning("加载装备配置失败: " + e.getMessage());
        }
    }
    
    /**
     * 加载粒子效果配置
     */
    private void loadParticleEffectsConfig(CustomMonster monster, ConfigurationSection section) {
        if (section == null) return;
        
        try {
            Map<String, ParticleEffect> effects = new HashMap<>();
            
            for (String trigger : section.getKeys(false)) {
                ConfigurationSection effectSection = section.getConfigurationSection(trigger);
                if (effectSection != null) {
                    ParticleEffect effect = new ParticleEffect();
                    
                    // 基础属性
                    String particleType = effectSection.getString("type", "FLAME");
                    try {
                        effect.setParticleType(org.bukkit.Particle.valueOf(particleType.toUpperCase()));
                    } catch (IllegalArgumentException e) {
                        effect.setParticleType(org.bukkit.Particle.FLAME);
                    }
                    
                    effect.setCount(effectSection.getInt("count", 10));
                    effect.setSpread(effectSection.getDouble("spread", 1.0));
                    effect.setDuration(effectSection.getLong("duration", 1000));
                    effect.setInterval(effectSection.getLong("interval", 100));
                    
                    effects.put(trigger, effect);
                }
            }
            
            monster.setParticleEffects(effects);
            
        } catch (Exception e) {
            logger.warning("加载粒子效果配置失败: " + e.getMessage());
        }
    }
    
    /**
     * 加载奖励配置
     */
    private void loadRewardsConfig(CustomMonster monster, ConfigurationSection section) {
        if (section == null) return;
        
        try {
            RewardConfig rewards = new RewardConfig();
            
            // 金钱奖励
            rewards.setMoney(section.getDouble("money", 0.0));
            rewards.setMoneyMin(section.getDouble("money_min", 0.0));
            rewards.setMoneyMax(section.getDouble("money_max", 0.0));
            rewards.setRandomMoney(section.getBoolean("random_money", false));
            
            // 经验奖励
            rewards.setExperience(section.getInt("experience", 0));
            rewards.setExperienceMin(section.getInt("experience_min", 0));
            rewards.setExperienceMax(section.getInt("experience_max", 0));
            rewards.setRandomExperience(section.getBoolean("random_experience", false));
            
            monster.setRewards(rewards);
            
        } catch (Exception e) {
            logger.warning("加载奖励配置失败: " + e.getMessage());
        }
    }
    
    /**
     * 保存自定义怪物到配置文件
     * 
     * @param monster 自定义怪物
     * @return 是否保存成功
     */
    public boolean saveCustomMonster(CustomMonster monster) {
        if (monster == null || monster.getName() == null) {
            return false;
        }
        
        try {
            // 更新缓存
            customMonsters.put(monster.getName(), monster);
            
            // 保存到配置文件
            saveMonsterToConfig(monster);
            
            // 保存配置文件
            config.save(configFile);
            
            logger.info("自定义怪物 '" + monster.getName() + "' 保存成功");
            return true;
            
        } catch (Exception e) {
            logger.severe("保存自定义怪物失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 将怪物保存到配置节点
     */
    private void saveMonsterToConfig(CustomMonster monster) {
        String path = MONSTERS_PATH + "." + monster.getName();
        
        // 基础信息
        config.set(path + ".id", monster.getId());
        config.set(path + ".display_name", monster.getDisplayName());
        config.set(path + ".entity_type", monster.getEntityType().name());
        config.set(path + ".description", monster.getDescription());
        
        // 属性
        config.set(path + ".health", monster.getHealth());
        config.set(path + ".damage", monster.getDamage());
        config.set(path + ".speed", monster.getSpeed());
        config.set(path + ".armor", monster.getArmor());
        config.set(path + ".armor_toughness", monster.getArmorToughness());
        config.set(path + ".knockback_resistance", monster.getKnockbackResistance());
        
        // 继承技能
        config.set(path + ".inherited_skills", monster.getInheritedSkills());
        
        // 技能参数
        if (!monster.getSkillParameters().isEmpty()) {
            for (Map.Entry<String, Object> entry : monster.getSkillParameters().entrySet()) {
                config.set(path + ".skill_parameters." + entry.getKey(), entry.getValue());
            }
        }
        
        // 装备
        saveEquipmentToConfig(monster, path + ".equipment");
        
        // 粒子效果
        saveParticleEffectsToConfig(monster, path + ".particle_effects");
        
        // 奖励
        saveRewardsToConfig(monster, path + ".rewards");
        
        // 元数据
        config.set(path + ".creator", monster.getCreator());
        config.set(path + ".create_time", monster.getCreateTime());
        config.set(path + ".last_modified", monster.getLastModified());
        config.set(path + ".enabled", monster.isEnabled());
    }
    
    /**
     * 保存装备配置到配置文件
     */
    private void saveEquipmentToConfig(CustomMonster monster, String basePath) {
        if (monster.getWeapon() != null) {
            config.set(basePath + ".weapon", monster.getWeapon().name());
        }
        if (monster.getHelmet() != null) {
            config.set(basePath + ".helmet", monster.getHelmet().name());
        }
        if (monster.getChestplate() != null) {
            config.set(basePath + ".chestplate", monster.getChestplate().name());
        }
        if (monster.getLeggings() != null) {
            config.set(basePath + ".leggings", monster.getLeggings().name());
        }
        if (monster.getBoots() != null) {
            config.set(basePath + ".boots", monster.getBoots().name());
        }
        
        // 附魔
        if (!monster.getWeaponEnchantments().isEmpty()) {
            for (Map.Entry<String, Integer> entry : monster.getWeaponEnchantments().entrySet()) {
                config.set(basePath + ".enchantments." + entry.getKey(), entry.getValue());
            }
        }
    }
    
    /**
     * 保存粒子效果配置到配置文件
     */
    private void saveParticleEffectsToConfig(CustomMonster monster, String basePath) {
        for (Map.Entry<String, ParticleEffect> entry : monster.getParticleEffects().entrySet()) {
            String effectPath = basePath + "." + entry.getKey();
            ParticleEffect effect = entry.getValue();
            
            config.set(effectPath + ".type", effect.getParticleType().name());
            config.set(effectPath + ".count", effect.getCount());
            config.set(effectPath + ".spread", effect.getSpread());
            config.set(effectPath + ".duration", effect.getDuration());
            config.set(effectPath + ".interval", effect.getInterval());
        }
    }
    
    /**
     * 保存奖励配置到配置文件
     */
    private void saveRewardsToConfig(CustomMonster monster, String basePath) {
        RewardConfig rewards = monster.getRewards();
        if (rewards == null) return;
        
        config.set(basePath + ".money", rewards.getMoney());
        config.set(basePath + ".money_min", rewards.getMoneyMin());
        config.set(basePath + ".money_max", rewards.getMoneyMax());
        config.set(basePath + ".random_money", rewards.isRandomMoney());
        
        config.set(basePath + ".experience", rewards.getExperience());
        config.set(basePath + ".experience_min", rewards.getExperienceMin());
        config.set(basePath + ".experience_max", rewards.getExperienceMax());
        config.set(basePath + ".random_experience", rewards.isRandomExperience());
    }
    
    /**
     * 删除自定义怪物
     * 
     * @param name 怪物名称
     * @return 是否删除成功
     */
    public boolean deleteCustomMonster(String name) {
        if (name == null || !customMonsters.containsKey(name)) {
            return false;
        }
        
        try {
            // 从缓存中移除
            customMonsters.remove(name);
            
            // 从配置文件中移除
            config.set(MONSTERS_PATH + "." + name, null);
            
            // 保存配置文件
            config.save(configFile);
            
            logger.info("自定义怪物 '" + name + "' 删除成功");
            return true;
            
        } catch (Exception e) {
            logger.severe("删除自定义怪物失败: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
    
    /**
     * 获取自定义怪物
     * 
     * @param name 怪物名称
     * @return 自定义怪物，如果不存在返回null
     */
    public CustomMonster getCustomMonster(String name) {
        return customMonsters.get(name);
    }
    
    /**
     * 获取所有自定义怪物
     * 
     * @return 自定义怪物映射
     */
    public Map<String, CustomMonster> getAllCustomMonsters() {
        return new HashMap<>(customMonsters);
    }
    
    /**
     * 获取启用的自定义怪物
     * 
     * @return 启用的自定义怪物列表
     */
    public List<CustomMonster> getEnabledCustomMonsters() {
        return customMonsters.values().stream()
                .filter(CustomMonster::isEnabled)
                .collect(java.util.stream.Collectors.toList());
    }
    
    /**
     * 检查怪物名称是否已存在
     * 
     * @param name 怪物名称
     * @return 是否存在
     */
    public boolean exists(String name) {
        return customMonsters.containsKey(name);
    }
    
    /**
     * 获取配置设置
     * 
     * @param key 设置键
     * @param defaultValue 默认值
     * @return 设置值
     */
    public Object getSetting(String key, Object defaultValue) {
        return config.get(SETTINGS_PATH + "." + key, defaultValue);
    }
    
    /**
     * 设置配置设置
     * 
     * @param key 设置键
     * @param value 设置值
     */
    public void setSetting(String key, Object value) {
        config.set(SETTINGS_PATH + "." + key, value);
        try {
            config.save(configFile);
        } catch (IOException e) {
            logger.warning("保存设置失败: " + e.getMessage());
        }
    }
    
    /**
     * 重新加载配置文件
     */
    public void reloadConfig() {
        try {
            config = YamlConfiguration.loadConfiguration(configFile);
            loadCustomMonsters();
            logger.info("Custom.yml配置文件重新加载完成");
        } catch (Exception e) {
            logger.severe("重新加载配置文件失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 保存配置文件
     */
    public void saveConfig() {
        try {
            config.save(configFile);
        } catch (IOException e) {
            logger.severe("保存配置文件失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
