package org.Ver_zhzh.deathZombieV4.commands;

import org.bukkit.command.Command;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.util.StringUtil;
import org.Ver_zhzh.deathZombieV4.DeathZombieV4;

import java.util.*;

/**
 * CZM命令的Tab补全器
 * 提供中文名称补全，不显示ID序列
 */
public class CZMTabCompleter implements TabCompleter {

    private final DeathZombieV4 plugin;
    
    // CZM子命令列表（包含IDZ命令）
    private final List<String> CZM_COMMANDS = Arrays.asList(
        "zombie", "other", "npc", "gui", "spawnmode", "help",
        "make", "edit", "delete", "remove", "list", "spawn", "reload", "test", "status", "skills"
    );
    
    // 怪物中文名称到ID的映射 (package-private for CZMCommandExecutor access)
    final Map<String, String> zombieNameToId = new HashMap<>();
    final Map<String, String> entityNameToId = new HashMap<>();
    final Map<String, String> npcNameToId = new HashMap<>();

    public CZMTabCompleter(DeathZombieV4 plugin) {
        this.plugin = plugin;
        initializeMonsterMappings();
    }

    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        List<String> completions = new ArrayList<>();

        if (args.length == 1) {
            // 第一个参数是子命令
            StringUtil.copyPartialMatches(args[0], CZM_COMMANDS, completions);
        } else if (args.length == 2) {
            // 第二个参数根据子命令类型补全中文名称
            String subCommand = args[0].toLowerCase();
            
            switch (subCommand) {
                case "zombie":
                    // 补全僵尸中文名称
                    List<String> zombieNames = new ArrayList<>(zombieNameToId.keySet());
                    StringUtil.copyPartialMatches(args[1], zombieNames, completions);
                    break;
                    
                case "other":
                    // 补全实体中文名称
                    List<String> entityNames = new ArrayList<>(entityNameToId.keySet());
                    StringUtil.copyPartialMatches(args[1], entityNames, completions);
                    break;
                    
                case "npc":
                    // 补全NPC中文名称
                    List<String> npcNames = new ArrayList<>(npcNameToId.keySet());
                    StringUtil.copyPartialMatches(args[1], npcNames, completions);
                    break;

                case "spawnmode":
                    // 补全系统类型
                    List<String> systemTypes = Arrays.asList("Zombie", "Entity");
                    StringUtil.copyPartialMatches(args[1], systemTypes, completions);
                    break;

                case "make":
                    // IDZ make命令补全
                    if ("gui".equals(args[1])) {
                        // 如果是gui，不需要更多补全
                    } else {
                        // 提示输入怪物名称
                        completions.add("<怪物名称>");
                    }
                    break;

                case "edit":
                case "delete":
                case "remove":
                case "spawn":
                    // IDZ命令补全 - 这里需要从IDZ系统获取自定义怪物列表
                    // 暂时添加占位符，后续需要集成
                    completions.add("<自定义怪物名称>");
                    break;

                case "list":
                    // 页码补全
                    completions.add("<页码>");
                    break;

                case "test":
                    // 测试类型补全
                    List<String> testTypes = Arrays.asList("quick", "full");
                    StringUtil.copyPartialMatches(args[1], testTypes, completions);
                    break;

                case "status":
                    // status命令不需要参数
                    break;

                case "skills":
                    // 技能统计类型补全
                    List<String> skillTypes = Arrays.asList("summary", "detail", "validate");
                    StringUtil.copyPartialMatches(args[1], skillTypes, completions);
                    break;

                default:
                    // 其他子命令不需要补全
                    break;
            }
        } else if (args.length == 3) {
            // 第三个参数
            String subCommand = args[0].toLowerCase();
            if ("spawnmode".equals(subCommand)) {
                // 补全模式类型
                List<String> modeTypes = Arrays.asList("User", "Normal", "InGame");
                StringUtil.copyPartialMatches(args[2], modeTypes, completions);
            } else if ("make".equals(subCommand) && !"gui".equals(args[1])) {
                // IDZ make命令的实体类型补全
                List<String> entityTypes = Arrays.asList(
                    "ZOMBIE", "SKELETON", "SPIDER", "CREEPER", "ENDERMAN",
                    "BLAZE", "WITCH", "PILLAGER", "EVOKER", "RAVAGER",
                    "ZOMBIFIED_PIGLIN", "PIGLIN", "HOGLIN", "ZOGLIN"
                );
                StringUtil.copyPartialMatches(args[2], entityTypes, completions);
            }
        } else if (args.length == 4) {
            // 第四个参数
            String subCommand = args[0].toLowerCase();
            if ("spawnmode".equals(subCommand)) {
                // 补全操作类型
                List<String> actions = Arrays.asList("on", "off");
                StringUtil.copyPartialMatches(args[3], actions, completions);
            }
        }

        return completions;
    }

    /**
     * 初始化怪物名称映射
     */
    private void initializeMonsterMappings() {
        // 僵尸类型 (id系列) - 只显示中文名称
        zombieNameToId.put("普通僵尸", "id1");
        zombieNameToId.put("小僵尸", "id2");
        zombieNameToId.put("路障僵尸", "id3");
        zombieNameToId.put("钻斧僵尸", "id4");
        zombieNameToId.put("剧毒僵尸", "id5");
        zombieNameToId.put("双生僵尸", "id6");
        zombieNameToId.put("骷髅僵尸", "id7");
        zombieNameToId.put("武装僵尸", "id8");
        zombieNameToId.put("肥胖僵尸", "id9");
        zombieNameToId.put("法师僵尸", "id10");
        zombieNameToId.put("自爆僵尸", "id11");
        zombieNameToId.put("毒箭僵尸", "id12");
        zombieNameToId.put("电击僵尸", "id13");
        zombieNameToId.put("冰冻僵尸", "id14");
        zombieNameToId.put("暗影僵尸", "id15");
        zombieNameToId.put("毁灭僵尸", "id16");
        zombieNameToId.put("雷霆僵尸", "id17");
        zombieNameToId.put("变异科学家", "id18");
        zombieNameToId.put("变异法师", "id19");
        zombieNameToId.put("气球僵尸", "id20");
        zombieNameToId.put("迷雾僵尸", "id21");
        zombieNameToId.put("变异雷霆僵尸", "id22");
        zombieNameToId.put("终极毁灭僵尸", "id23");
        zombieNameToId.put("变异暗影僵尸", "id24");
        zombieNameToId.put("变异博士", "id25");

        // 实体类型 (idc系列) - 只显示中文名称
        entityNameToId.put("变异僵尸01", "idc1");
        entityNameToId.put("变异僵尸02", "idc2");
        entityNameToId.put("变异烈焰人", "idc3");
        entityNameToId.put("变异爬行者", "idc4");
        entityNameToId.put("变异末影螨", "idc5");
        entityNameToId.put("变异蜘蛛", "idc6");
        entityNameToId.put("灾厄卫道士", "idc7");
        entityNameToId.put("灾厄唤魔者", "idc8");
        entityNameToId.put("灾厄劫掠兽", "idc9");
        entityNameToId.put("变异僵尸马", "idc10");
        entityNameToId.put("变异岩浆怪", "idc11");
        entityNameToId.put("变异尸壳", "idc12");  // 修复：应该是变异尸壳，不是变异骷髅
        entityNameToId.put("变异僵尸3", "idc13");
        entityNameToId.put("变异僵尸04", "idc14");
        entityNameToId.put("鲜血猪灵", "idc15");  // 修复：idc15对应鲜血猪灵
        entityNameToId.put("暗影潜影贝", "idc16");  // 修复：idc16对应暗影潜影贝
        entityNameToId.put("变异雪傀儡", "idc17");  // 修复：去掉"2"
        entityNameToId.put("变异铁傀儡", "idc18");
        entityNameToId.put("变异僵尸Max", "idc19");
        entityNameToId.put("灵魂坚守者", "idc20");
        entityNameToId.put("凋零领主", "idc21");
        entityNameToId.put("异变之王", "idc22");

        // NPC类型 (idn系列) - 只显示中文名称
        npcNameToId.put("感染者史蒂夫", "idn1");
        npcNameToId.put("感染者艾利克斯", "idn2");
        npcNameToId.put("感染者农民", "idn3");
        npcNameToId.put("感染者居民", "idn4");
        npcNameToId.put("感染猪", "idn5");
    }

    /**
     * 根据中文名称获取僵尸ID
     */
    public String getZombieIdByName(String name) {
        return zombieNameToId.get(name);
    }

    /**
     * 根据中文名称获取实体ID
     */
    public String getEntityIdByName(String name) {
        return entityNameToId.get(name);
    }

    /**
     * 根据中文名称获取NPC ID
     */
    public String getNpcIdByName(String name) {
        return npcNameToId.get(name);
    }

    /**
     * 获取所有僵尸中文名称
     */
    public Set<String> getAllZombieNames() {
        return zombieNameToId.keySet();
    }

    /**
     * 获取所有实体中文名称
     */
    public Set<String> getAllEntityNames() {
        return entityNameToId.keySet();
    }

    /**
     * 获取所有NPC中文名称
     */
    public Set<String> getAllNpcNames() {
        return npcNameToId.keySet();
    }
}
