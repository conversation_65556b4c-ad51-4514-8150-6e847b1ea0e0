package org.Ver_zhzh.deathZombieV4.commands;

import org.Ver_zhzh.deathZombieV4.DeathZombieV4;
import org.Ver_zhzh.deathZombieV4.utils.ZombieHelper;
import org.Ver_zhzh.customZombie.UserMaker.IDZSystemManager;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.configuration.file.YamlConfiguration;
import java.io.File;
import org.bukkit.entity.Zombie;
import java.util.Map;

/**
 * 处理/czm命令的执行器类
 * 现在集成了IDZ自定义怪物系统
 */
public class CZMCommandExecutor implements CommandExecutor {

    private final DeathZombieV4 plugin;
    private final ZombieHelper zombieHelper;
    private final CZMTabCompleter tabCompleter;
    private IDZSystemManager idzSystemManager;

    /**
     * 构造函数
     *
     * @param plugin DeathZombieV4插件实例
     */
    public CZMCommandExecutor(DeathZombieV4 plugin) {
        this.plugin = plugin;
        this.zombieHelper = plugin.getZombieHelper();
        this.tabCompleter = new CZMTabCompleter(plugin);

        // 初始化IDZ系统管理器
        this.idzSystemManager = new IDZSystemManager(plugin);
        if (!this.idzSystemManager.initialize()) {
            plugin.getLogger().warning("IDZ系统初始化失败，IDZ功能将不可用");
            this.idzSystemManager = null;
        }
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        // 允许非玩家（控制台、命令方块等）使用此命令
        Player player = null;
        if (sender instanceof Player) {
            player = (Player) sender;
        }

        if (args.length < 1) {
            sendHelpMessage(sender);
            return true;
        }

        // 检查是否为IDZ相关命令
        String firstArg = args[0].toLowerCase();
        if (isIDZCommand(firstArg)) {
            return handleIDZCommand(sender, args);
        }

        switch (firstArg) {
            case "zombie":
                if (args.length < 2) {
                    sender.sendMessage(ChatColor.RED + "请指定僵尸名称或ID! 用法: /czm zombie <名称|ID>");
                    sender.sendMessage(ChatColor.YELLOW + "示例: /czm zombie 普通僵尸 或 /czm zombie id1");
                    return true;
                }

                if (player == null) {
                    sender.sendMessage(ChatColor.RED + "此子命令需要玩家执行，因为需要获取玩家位置!");
                    return true;
                }

                String zombieInput = args[1];
                String zombieId = convertZombieNameToId(zombieInput);

                if (zombieId == null) {
                    sender.sendMessage(ChatColor.RED + "未知的僵尸名称或ID: " + zombieInput);
                    sender.sendMessage(ChatColor.YELLOW + "请使用Tab补全查看可用的僵尸名称");
                    return true;
                }

                Zombie zombie = zombieHelper.spawnCustomZombie(player.getLocation(), zombieId);

                if (zombie != null) {
                    String zombieDisplayName = getZombieDisplayName(zombieInput, zombieId);
                    sender.sendMessage(ChatColor.GREEN + "成功生成自定义僵尸: " + zombieDisplayName);
                } else {
                    sender.sendMessage(ChatColor.RED + "生成自定义僵尸失败");
                }
                return true;

            case "other":
                if (args.length < 2) {
                    sender.sendMessage(ChatColor.RED + "请指定实体名称或ID! 用法: /czm other <名称|IDC>");
                    sender.sendMessage(ChatColor.YELLOW + "示例: /czm other 变异僵尸01 或 /czm other idc1");
                    return true;
                }

                if (player == null) {
                    sender.sendMessage(ChatColor.RED + "此子命令需要玩家执行，因为需要获取玩家位置!");
                    return true;
                }

                String entityInput = args[1];
                String entityId = convertEntityNameToId(entityInput);

                if (entityId == null) {
                    sender.sendMessage(ChatColor.RED + "未知的实体名称或ID: " + entityInput);
                    sender.sendMessage(ChatColor.YELLOW + "请使用Tab补全查看可用的实体名称");
                    return true;
                }

                // 调用CustomZombie的spawnOtherEntity方法，传入Player参数
                zombieHelper.getCustomZombie().spawnOtherEntity(player, entityId);
                String entityDisplayName = getEntityDisplayName(entityInput, entityId);
                sender.sendMessage(ChatColor.GREEN + "成功生成实体: " + entityDisplayName);
                return true;

            case "npc":
                if (args.length < 2) {
                    sender.sendMessage(ChatColor.RED + "请指定NPC名称或ID! 用法: /czm npc <名称|IDN>");
                    sender.sendMessage(ChatColor.YELLOW + "示例: /czm npc 感染者史蒂夫 或 /czm npc idn1");
                    return true;
                }

                if (player == null) {
                    sender.sendMessage(ChatColor.RED + "此子命令需要玩家执行，因为需要获取玩家位置!");
                    return true;
                }

                // 检查Citizens插件是否可用
                if (Bukkit.getPluginManager().getPlugin("Citizens") == null) {
                    sender.sendMessage(ChatColor.RED + "无法生成NPC! 服务器未安装Citizens插件。");
                    return true;
                }

                String npcInput = args[1];
                String npcId = convertNpcNameToId(npcInput);

                if (npcId == null) {
                    sender.sendMessage(ChatColor.RED + "未知的NPC名称或ID: " + npcInput);
                    sender.sendMessage(ChatColor.YELLOW + "请使用Tab补全查看可用的NPC名称");
                    return true;
                }

                boolean npcSuccess = spawnNPC(player, npcId);

                if (npcSuccess) {
                    String npcDisplayName = getNpcDisplayName(npcInput, npcId);
                    sender.sendMessage(ChatColor.GREEN + "成功生成NPC: " + npcDisplayName);
                } else {
                    sender.sendMessage(ChatColor.RED + "生成NPC失败: " + npcId);
                }
                return true;

            case "gui":
                if (player == null) {
                    sender.sendMessage(ChatColor.RED + "此子命令需要玩家执行，因为需要打开GUI界面!");
                    return true;
                }

                // 尝试打开GUI菜单
                boolean guiOpened = zombieHelper.openGUI(player);
                if (!guiOpened) {
                    sender.sendMessage(ChatColor.RED + "GUI管理器未初始化，无法打开GUI界面");
                }
                return true;

            case "spawnmode":
                // spawnmode命令需要管理员权限
                if (player != null && !player.hasPermission("deathzombiev4.admin")) {
                    sender.sendMessage(ChatColor.RED + "你需要管理员权限来使用此指令！");
                    return true;
                }
                if (args.length < 4) {
                    sender.sendMessage(ChatColor.RED + "用法: /czm spawnmode <Zombie|Entity> <User|Normal|InGame> <on|off>");
                    sender.sendMessage(ChatColor.YELLOW + "Zombie - 僵尸系统模式切换");
                    sender.sendMessage(ChatColor.YELLOW + "Entity - 实体系统模式切换");
                    sender.sendMessage(ChatColor.YELLOW + "User - 用户自定义模式");
                    sender.sendMessage(ChatColor.YELLOW + "Normal - 默认模式");
                    sender.sendMessage(ChatColor.YELLOW + "InGame - 游戏中用户实体生成开关（仅Entity系统）");
                    return true;
                }
                return handleSpawnModeCommand(sender, args[1], args[2], args[3]);

            case "help":
            default:
                sendHelpMessage(sender);
                return true;
        }
    }

    /**
     * 根据NPC ID生成相应的NPC
     *
     * @param player 玩家
     * @param npcId NPC ID
     * @return 是否成功生成
     */
    private boolean spawnNPC(Player player, String npcId) {
        switch (npcId) {
            case "idn1":
                // 感染者1
                return zombieHelper.createInfectedNPC1(player.getLocation());
            case "idn2":
                // 感染者2号
                return zombieHelper.createInfectedNPC2(player.getLocation());
            case "idn3":
                // 感染者农民
                return zombieHelper.createInfectedFarmer(player.getLocation());
            case "idn4":
                // 感染者居民
                return zombieHelper.createInfectedResident(player.getLocation());
            case "idn5":
                // 感染猪
                return zombieHelper.createInfectedPig(player.getLocation());
            default:
                return false;
        }
    }

    /**
     * 处理SpawnMode指令 - 快速切换僵尸/实体系统模式
     */
    private boolean handleSpawnModeCommand(CommandSender sender, String systemType, String modeType, String action) {
        // 验证参数
        if (!systemType.equalsIgnoreCase("Zombie") && !systemType.equalsIgnoreCase("Entity")) {
            sender.sendMessage(ChatColor.RED + "系统类型必须是 'Zombie' 或 'Entity'！");
            return true;
        }

        if (!modeType.equalsIgnoreCase("User") && !modeType.equalsIgnoreCase("Normal") && !modeType.equalsIgnoreCase("InGame")) {
            sender.sendMessage(ChatColor.RED + "模式类型必须是 'User'、'Normal' 或 'InGame'！");
            return true;
        }

        if (!action.equalsIgnoreCase("on") && !action.equalsIgnoreCase("off")) {
            sender.sendMessage(ChatColor.RED + "操作必须是 'on' 或 'off'！");
            return true;
        }

        boolean isZombieSystem = systemType.equalsIgnoreCase("Zombie");
        boolean isUserMode = modeType.equalsIgnoreCase("User");
        boolean isInGameMode = modeType.equalsIgnoreCase("InGame");
        boolean enableMode = action.equalsIgnoreCase("on");

        try {
            if (isZombieSystem) {
                // 僵尸系统不支持InGame模式
                if (isInGameMode) {
                    sender.sendMessage(ChatColor.RED + "僵尸系统不支持InGame模式！");
                    return true;
                }
                // 处理僵尸系统模式切换
                return handleZombieSystemModeSwitch(sender, isUserMode, enableMode);
            } else {
                // 处理实体系统模式切换
                return handleEntitySystemModeSwitch(sender, isUserMode, isInGameMode, enableMode);
            }
        } catch (Exception e) {
            sender.sendMessage(ChatColor.RED + "模式切换失败: " + e.getMessage());
            plugin.getLogger().warning("SpawnMode指令执行失败: " + e.getMessage());
            return true;
        }
    }

    /**
     * 处理僵尸系统模式切换
     */
    private boolean handleZombieSystemModeSwitch(CommandSender sender, boolean isUserMode, boolean enableMode) {
        try {
            // 读取zombie.yml配置
            File zombieConfigFile = new File(plugin.getDataFolder(), "zombie.yml");
            if (!zombieConfigFile.exists()) {
                sender.sendMessage(ChatColor.RED + "zombie.yml配置文件不存在！");
                return true;
            }

            YamlConfiguration zombieConfig = YamlConfiguration.loadConfiguration(zombieConfigFile);

            if (enableMode) {
                // 开启指定模式，关闭另一个模式
                if (isUserMode) {
                    zombieConfig.set("system_settings.use_user_custom_settings", true);
                    zombieConfig.set("system_settings.use_default_settings", false);
                    sender.sendMessage(ChatColor.GREEN + "已切换僵尸系统到用户自定义模式！");
                } else {
                    zombieConfig.set("system_settings.use_default_settings", true);
                    zombieConfig.set("system_settings.use_user_custom_settings", false);
                    sender.sendMessage(ChatColor.GREEN + "已切换僵尸系统到默认模式！");
                }
            } else {
                // 关闭指定模式，开启另一个模式
                if (isUserMode) {
                    zombieConfig.set("system_settings.use_user_custom_settings", false);
                    zombieConfig.set("system_settings.use_default_settings", true);
                    sender.sendMessage(ChatColor.GREEN + "已关闭僵尸用户自定义模式，切换到默认模式！");
                } else {
                    zombieConfig.set("system_settings.use_default_settings", false);
                    zombieConfig.set("system_settings.use_user_custom_settings", true);
                    sender.sendMessage(ChatColor.GREEN + "已关闭僵尸默认模式，切换到用户自定义模式！");
                }
            }

            // 保存配置文件
            zombieConfig.save(zombieConfigFile);

            // 重载双僵尸系统配置
            if (plugin.getDualZombieSystemManager() != null) {
                plugin.getDualZombieSystemManager().reloadConfig();
                // 只在debug模式下输出到日志，不再给玩家显示
                if (plugin.getConfig().getBoolean("debug", false)) {
                    plugin.getLogger().info("僵尸系统配置已重新加载");
                }
            }

            return true;
        } catch (Exception e) {
            sender.sendMessage(ChatColor.RED + "僵尸系统模式切换失败: " + e.getMessage());
            plugin.getLogger().warning("僵尸系统模式切换失败: " + e.getMessage());
            return true;
        }
    }

    /**
     * 处理实体系统模式切换
     */
    private boolean handleEntitySystemModeSwitch(CommandSender sender, boolean isUserMode, boolean isInGameMode, boolean enableMode) {
        try {
            // 读取entity.yml配置
            File entityConfigFile = new File(plugin.getDataFolder(), "entity.yml");
            if (!entityConfigFile.exists()) {
                sender.sendMessage(ChatColor.RED + "entity.yml配置文件不存在！");
                return true;
            }

            YamlConfiguration entityConfig = YamlConfiguration.loadConfiguration(entityConfigFile);

            if (isInGameMode) {
                // 处理游戏中用户实体生成开关
                entityConfig.set("system_settings.enable_in_game_user_entities", enableMode);
                if (enableMode) {
                    sender.sendMessage(ChatColor.GREEN + "已启用游戏中用户自定义实体生成！");
                } else {
                    sender.sendMessage(ChatColor.GREEN + "已禁用游戏中用户自定义实体生成！");
                }
            } else if (enableMode) {
                // 开启指定模式，关闭另一个模式
                if (isUserMode) {
                    entityConfig.set("system_settings.use_user_custom_settings", true);
                    entityConfig.set("system_settings.use_default_settings", false);
                    sender.sendMessage(ChatColor.GREEN + "已切换实体系统到用户自定义模式！");
                } else {
                    entityConfig.set("system_settings.use_default_settings", true);
                    entityConfig.set("system_settings.use_user_custom_settings", false);
                    sender.sendMessage(ChatColor.GREEN + "已切换实体系统到默认模式！");
                }
            } else {
                // 关闭指定模式，开启另一个模式
                if (isUserMode) {
                    entityConfig.set("system_settings.use_user_custom_settings", false);
                    entityConfig.set("system_settings.use_default_settings", true);
                    sender.sendMessage(ChatColor.GREEN + "已关闭实体用户自定义模式，切换到默认模式！");
                } else {
                    entityConfig.set("system_settings.use_default_settings", false);
                    entityConfig.set("system_settings.use_user_custom_settings", true);
                    sender.sendMessage(ChatColor.GREEN + "已关闭实体默认模式，切换到用户自定义模式！");
                }
            }

            // 保存配置文件
            entityConfig.save(entityConfigFile);

            // 重载实体系统配置
            if (plugin.getZombieHelper() != null &&
                plugin.getZombieHelper().getCustomZombie() != null &&
                plugin.getZombieHelper().getCustomZombie().getEntityIntegration() != null) {
                plugin.getZombieHelper().getCustomZombie().getEntityIntegration().reloadConfig();
                // 只在debug模式下输出到日志，不再给玩家显示
                if (plugin.getConfig().getBoolean("debug", false)) {
                    plugin.getLogger().info("实体系统配置已重新加载");
                }
            }

            return true;
        } catch (Exception e) {
            sender.sendMessage(ChatColor.RED + "实体系统模式切换失败: " + e.getMessage());
            plugin.getLogger().warning("实体系统模式切换失败: " + e.getMessage());
            return true;
        }
    }

    /**
     * 发送帮助信息
     *
     * @param sender 命令发送者
     */
    private void sendHelpMessage(CommandSender sender) {
        sender.sendMessage(ChatColor.GOLD + "======= CustomZombie 帮助 =======");

        // 原有功能
        sender.sendMessage(ChatColor.AQUA + "=== 原版怪物生成 ===");
        sender.sendMessage(ChatColor.GREEN + "/czm zombie <名称|ID> - 生成指定的僵尸 (需要玩家执行)");
        sender.sendMessage(ChatColor.YELLOW + "  支持中文名称: 普通僵尸, 小僵尸, 路障僵尸, 钻斧僵尸...");
        sender.sendMessage(ChatColor.YELLOW + "  示例: /czm zombie 普通僵尸 或 /czm zombie id1");
        sender.sendMessage(ChatColor.GREEN + "/czm other <名称|IDC> - 生成指定的其他实体 (需要玩家执行)");
        sender.sendMessage(ChatColor.YELLOW + "  支持中文名称: 变异僵尸01, 变异僵尸02, 变异烈焰人...");
        sender.sendMessage(ChatColor.YELLOW + "  示例: /czm other 变异僵尸01 或 /czm other idc1");
        sender.sendMessage(ChatColor.GREEN + "/czm npc <名称|IDN> - 生成指定的NPC (需要玩家执行)");
        sender.sendMessage(ChatColor.YELLOW + "  支持中文名称: 感染者史蒂夫, 感染者艾利克斯, 感染者农民...");
        sender.sendMessage(ChatColor.YELLOW + "  示例: /czm npc 感染者史蒂夫 或 /czm npc idn1");
        sender.sendMessage(ChatColor.GREEN + "/czm gui - 打开自定义僵尸GUI菜单 (需要玩家执行)");
        sender.sendMessage(ChatColor.GREEN + "/czm spawnmode <Zombie|Entity> <User|Normal|InGame> <on|off> - 快速切换系统模式");

        // IDZ自定义怪物功能
        sender.sendMessage(ChatColor.AQUA + "=== IDZ自定义怪物系统 ===");
        sender.sendMessage(ChatColor.LIGHT_PURPLE + "/czm make <名称> [实体类型] - 创建新的自定义怪物");
        sender.sendMessage(ChatColor.LIGHT_PURPLE + "/czm make gui - 打开可视化编辑器");
        sender.sendMessage(ChatColor.LIGHT_PURPLE + "/czm edit <名称> - 查看怪物信息");
        sender.sendMessage(ChatColor.LIGHT_PURPLE + "/czm list [页码] - 查看自定义怪物列表");
        sender.sendMessage(ChatColor.LIGHT_PURPLE + "/czm spawn <名称> - 生成自定义怪物");
        sender.sendMessage(ChatColor.LIGHT_PURPLE + "/czm delete <名称> - 删除自定义怪物");
        sender.sendMessage(ChatColor.LIGHT_PURPLE + "/czm reload - 重新加载IDZ配置");

        sender.sendMessage(ChatColor.GREEN + "/czm help - 显示帮助信息");
        sender.sendMessage(ChatColor.GRAY + "提示: 使用Tab键可以自动补全名称");
    }

    /**
     * 转换僵尸名称到ID
     *
     * @param input 用户输入的名称或ID
     * @return 对应的ID，如果无效则返回null
     */
    private String convertZombieNameToId(String input) {
        // 如果输入已经是ID格式，直接返回
        if (input.toLowerCase().startsWith("id")) {
            return input.toLowerCase();
        }

        // 尝试从中文名称转换
        String id = tabCompleter.getZombieIdByName(input);
        return id;
    }

    /**
     * 转换实体名称到ID
     *
     * @param input 用户输入的名称或ID
     * @return 对应的ID，如果无效则返回null
     */
    private String convertEntityNameToId(String input) {
        // 如果输入已经是ID格式，直接返回
        if (input.toLowerCase().startsWith("idc")) {
            return input.toLowerCase();
        }

        // 尝试从中文名称转换
        String id = tabCompleter.getEntityIdByName(input);
        return id;
    }

    /**
     * 转换NPC名称到ID
     *
     * @param input 用户输入的名称或ID
     * @return 对应的ID，如果无效则返回null
     */
    private String convertNpcNameToId(String input) {
        // 如果输入已经是ID格式，直接返回
        if (input.toLowerCase().startsWith("idn")) {
            return input.toLowerCase();
        }

        // 尝试从中文名称转换
        String id = tabCompleter.getNpcIdByName(input);
        return id;
    }

    /**
     * 获取僵尸显示名称
     *
     * @param input 用户输入
     * @param id 实际ID
     * @return 显示名称
     */
    private String getZombieDisplayName(String input, String id) {
        // 如果输入是中文名称，直接返回
        if (!input.toLowerCase().startsWith("id")) {
            return input;
        }

        // 如果输入是ID，尝试转换为中文名称
        for (Map.Entry<String, String> entry : tabCompleter.zombieNameToId.entrySet()) {
            if (entry.getValue().equals(id)) {
                return entry.getKey() + " (" + id + ")";
            }
        }

        return id;
    }

    /**
     * 获取实体显示名称
     *
     * @param input 用户输入
     * @param id 实际ID
     * @return 显示名称
     */
    private String getEntityDisplayName(String input, String id) {
        // 如果输入是中文名称，直接返回
        if (!input.toLowerCase().startsWith("idc")) {
            return input;
        }

        // 如果输入是ID，尝试转换为中文名称
        for (Map.Entry<String, String> entry : tabCompleter.entityNameToId.entrySet()) {
            if (entry.getValue().equals(id)) {
                return entry.getKey() + " (" + id + ")";
            }
        }

        return id;
    }

    /**
     * 获取NPC显示名称
     *
     * @param input 用户输入
     * @param id 实际ID
     * @return 显示名称
     */
    private String getNpcDisplayName(String input, String id) {
        // 如果输入是中文名称，直接返回
        if (!input.toLowerCase().startsWith("idn")) {
            return input;
        }

        // 如果输入是ID，尝试转换为中文名称
        for (Map.Entry<String, String> entry : tabCompleter.npcNameToId.entrySet()) {
            if (entry.getValue().equals(id)) {
                return entry.getKey() + " (" + id + ")";
            }
        }

        return id;
    }

    /**
     * 检查是否为IDZ命令
     *
     * @param command 命令
     * @return 是否为IDZ命令
     */
    private boolean isIDZCommand(String command) {
        return "make".equals(command) || "edit".equals(command) ||
               "delete".equals(command) || "remove".equals(command) ||
               "list".equals(command) || "spawn".equals(command) ||
               "reload".equals(command);
    }

    /**
     * 处理IDZ命令
     *
     * @param sender 命令发送者
     * @param args 命令参数
     * @return 是否处理成功
     */
    private boolean handleIDZCommand(CommandSender sender, String[] args) {
        if (idzSystemManager == null) {
            sender.sendMessage(ChatColor.RED + "IDZ系统未初始化，无法使用IDZ功能");
            sender.sendMessage(ChatColor.YELLOW + "请联系管理员检查插件配置");
            return true;
        }

        return idzSystemManager.handleCommand(sender, args);
    }

    /**
     * 获取IDZ系统管理器
     *
     * @return IDZ系统管理器
     */
    public IDZSystemManager getIDZSystemManager() {
        return idzSystemManager;
    }

    /**
     * 关闭IDZ系统
     */
    public void shutdown() {
        if (idzSystemManager != null) {
            idzSystemManager.shutdown();
        }
    }
}
