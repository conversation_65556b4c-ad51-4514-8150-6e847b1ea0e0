package org.Ver_zhzh.deathZombieV4;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.Ver_zhzh.deathZombieV4.commands.CommandManager;
import org.Ver_zhzh.deathZombieV4.commands.CommandTabCompleter;
import org.Ver_zhzh.deathZombieV4.commands.CZMCommandExecutor;
import org.Ver_zhzh.deathZombieV4.commands.CZMTabCompleter;
import org.Ver_zhzh.deathZombieV4.game.DoorManager;
import org.Ver_zhzh.deathZombieV4.game.EquipmentConstants;
import org.Ver_zhzh.deathZombieV4.game.GameKitManager;
import org.Ver_zhzh.deathZombieV4.game.GameManager;
import org.Ver_zhzh.deathZombieV4.game.GameSessionManager;
import org.Ver_zhzh.deathZombieV4.game.LobbyManager;
import org.Ver_zhzh.deathZombieV4.game.LuckyBoxManager;
import org.Ver_zhzh.deathZombieV4.game.WindowManager;
import org.Ver_zhzh.deathZombieV4.gui.KitEditorGUI;
import org.Ver_zhzh.deathZombieV4.listeners.BlockPlaceListener;
import org.Ver_zhzh.deathZombieV4.listeners.BulletHitListener;
import org.Ver_zhzh.deathZombieV4.listeners.DoorInteractionListener;
import org.Ver_zhzh.deathZombieV4.listeners.EntityDropListener;
import org.Ver_zhzh.deathZombieV4.listeners.InventoryMoveListener;
import org.Ver_zhzh.deathZombieV4.listeners.ItemConsumeListener;
import org.Ver_zhzh.deathZombieV4.listeners.LuckyBoxInteractionListener;
import org.Ver_zhzh.deathZombieV4.listeners.PlayerDeathReviveListener;
import org.Ver_zhzh.deathZombieV4.listeners.PlayerDisplaySyncListener;
import org.Ver_zhzh.deathZombieV4.listeners.PlayerPvPListener;
import org.Ver_zhzh.deathZombieV4.listeners.PlayerStatusListener;
import org.Ver_zhzh.deathZombieV4.listeners.PowerButtonInteractionListener;
import org.Ver_zhzh.deathZombieV4.listeners.WindowRepairListener;
import org.Ver_zhzh.deathZombieV4.listeners.ZombieAttackListener;
import org.Ver_zhzh.deathZombieV4.listeners.HostileAIListener;
import org.Ver_zhzh.deathZombieV4.utils.ActionBarManager;
import org.Ver_zhzh.deathZombieV4.utils.DoorHologramManager;
import org.Ver_zhzh.deathZombieV4.utils.DoorNPCManager;
import org.Ver_zhzh.deathZombieV4.utils.GlobalLogManager;
import org.Ver_zhzh.deathZombieV4.utils.HologramHelper;
import org.Ver_zhzh.deathZombieV4.utils.HologramInteractionHandler;
import org.Ver_zhzh.deathZombieV4.utils.HologramLeaderboardManager;
import org.Ver_zhzh.deathZombieV4.utils.LeaderboardManager;
import org.Ver_zhzh.deathZombieV4.utils.HostileAIManager;
import org.Ver_zhzh.deathZombieV4.utils.MonsterIdMapper;
import org.Ver_zhzh.deathZombieV4.utils.PlayerDisplaySettingsManager;
import org.Ver_zhzh.deathZombieV4.utils.PlayerStatisticsManager;
import org.Ver_zhzh.deathZombieV4.utils.PlayerInteractionManager;
import org.Ver_zhzh.deathZombieV4.utils.PowerButtonEffectManager;
import org.Ver_zhzh.deathZombieV4.utils.RegionSelector;
import org.Ver_zhzh.deathZombieV4.utils.ScoreboardManager;
import org.Ver_zhzh.deathZombieV4.utils.ShootPluginHelper;
import org.Ver_zhzh.deathZombieV4.utils.ShopHologramManager;
import org.Ver_zhzh.deathZombieV4.utils.ZombieHelper;
import org.Ver_zhzh.deathZombieV4.utils.ZombieSpawnManager;
import org.Ver_zhzh.deathZombieV4.utils.MessageManager;
import org.Ver_zhzh.deathZombieV4.utils.BatchDeathDetector;
import org.Ver_zhzh.deathZombieV4.web.WebServer;
import org.bukkit.command.PluginCommand;
import org.bukkit.plugin.Plugin;
import org.bukkit.plugin.java.JavaPlugin;

/**
 * DeathZombieV4主类，实现了僵尸生存游戏的主要功能
 */
public class DeathZombieV4 extends JavaPlugin {

    private GameManager gameManager;
    private GameSessionManager gameSessionManager;
    private DoorManager doorManager;
    private WindowManager windowManager;
    private ScoreboardManager scoreboardManager;
    private ShootPluginHelper shootPluginHelper;
    private ZombieHelper zombieHelper;
    private ZombieSpawnManager zombieSpawnManager;
    private PlayerInteractionManager playerInteractionManager;
    private PlayerDisplaySettingsManager playerDisplaySettingsManager;
    private PlayerStatisticsManager playerStatisticsManager;
    private MonsterIdMapper monsterIdMapper;
    private GameKitManager gameKitManager;
    private LobbyManager lobbyManager;
    private LuckyBoxManager luckyBoxManager;
    private Plugin shootPlugin;
    private MessageManager messageManager;
    private KitEditorGUI kitEditorGUI;

    // CustomZombie实例
    private org.Ver_zhzh.customZombie.CustomZombie customZombie;

    // 双CustomZombie系统管理器
    private org.Ver_zhzh.customZombie.DualZombieSystemManager dualZombieSystemManager;

    // 依赖插件
    private WebServer webServer;
    private RegionSelector regionSelector;

    // 原有的NPC管理器（将被逐步替换）
    private DoorNPCManager doorNPCManager;

    // 新增的悬浮文字管理器
    private HologramHelper hologramHelper;
    private DoorHologramManager doorHologramManager;
    private ShopHologramManager shopHologramManager;
    private PowerButtonEffectManager powerButtonEffectManager;
    private HologramLeaderboardManager hologramLeaderboardManager;

    private PlayerDeathReviveListener playerDeathReviveListener;
    private ZombieAttackListener zombieAttackListener;
    private BulletHitListener bulletHitListener;
    private ActionBarManager actionBarManager;
    private GlobalLogManager logManager;
    private org.Ver_zhzh.deathZombieV4.listeners.EntityNameDisplayListener entityNameDisplayListener;
    private HostileAIManager hostileAIManager;
    private HostileAIListener hostileAIListener;
    private BatchDeathDetector batchDeathDetector;

    // IDZ系统相关
    private CZMCommandExecutor czmCommandExecutor;

    @Override
    public void onEnable() {
        // 保存默认配置
        saveDefaultConfig();

        // 初始化日志管理器
        this.logManager = new GlobalLogManager(this);

        // 初始化批量死亡检测器
        this.batchDeathDetector = new BatchDeathDetector(this);

        // 初始化消息管理器
        this.messageManager = new MessageManager(this);

        // 初始化游戏管理器
        this.gameManager = new GameManager(this);
        this.gameSessionManager = new GameSessionManager(this);
        this.doorManager = new DoorManager(this);
        this.windowManager = new WindowManager(this);
        this.shootPluginHelper = new ShootPluginHelper(this);
        this.zombieHelper = new ZombieHelper(this);
        this.playerInteractionManager = new PlayerInteractionManager(this);
        this.playerDisplaySettingsManager = new PlayerDisplaySettingsManager(this);
        this.playerStatisticsManager = new PlayerStatisticsManager(this);
        this.monsterIdMapper = new MonsterIdMapper(this);
        this.gameKitManager = new GameKitManager(this);

        // 初始化大厅管理器
        this.lobbyManager = new LobbyManager(this);
        getLogger().info("大厅管理器初始化完成");

        // 初始化幸运箱管理器
        this.luckyBoxManager = new LuckyBoxManager(this);
        getLogger().info("幸运箱管理器初始化完成");

        // 初始化EquipmentConstants
        EquipmentConstants.setPlugin(this);

        // 初始化KitEditorGUI
        this.kitEditorGUI = new KitEditorGUI(this);
        getLogger().info("初始装备编辑器GUI初始化完成");

        // 尝试获取Shoot插件
        if (getServer().getPluginManager().getPlugin("Shoot") != null) {
            this.shootPlugin = getServer().getPluginManager().getPlugin("Shoot");
            getLogger().info("成功连接到Shoot插件");
        } else {
            getLogger().warning("未找到Shoot插件，相关功能将不可用");
        }

        // 使用原生标题显示功能（Paper/Spigot 1.8+原生支持）
        getLogger().info("使用原生标题显示功能");

        // 初始化区域选择器
        this.regionSelector = new RegionSelector(this);

        // 初始化计分板管理器
        this.scoreboardManager = new ScoreboardManager(this);

        // 检查并重置所有处于ENDED状态的游戏
        resetEndedGames();

        // 初始化僵尸生成管理器
        this.zombieSpawnManager = new ZombieSpawnManager(this);

        // 设置计分板管理器到僵尸生成管理器
        this.zombieSpawnManager.setScoreboardManager(this.scoreboardManager);

        // 调试日志
        getLogger().info("已将ScoreboardManager设置到ZombieSpawnManager");

        // 检测DecentHolograms插件
        if (getServer().getPluginManager().getPlugin("DecentHolograms") != null) {
            getLogger().info("检测到DecentHolograms插件，将使用悬浮文字功能");

            // 初始化悬浮文字助手
            this.hologramHelper = new HologramHelper(this);

            // 初始化门悬浮文字管理器
            this.doorHologramManager = new DoorHologramManager(this);

            // 初始化购买点悬浮文字管理器
            this.shopHologramManager = new ShopHologramManager(this);

            // 初始化全局电源按钮效果管理器
            this.powerButtonEffectManager = new PowerButtonEffectManager(this);

            // 初始化全息排行榜管理器
            this.hologramLeaderboardManager = new HologramLeaderboardManager(this);
            getServer().getPluginManager().registerEvents(hologramLeaderboardManager, this);

            // 注册全息图交互处理器
            getServer().getPluginManager().registerEvents(new HologramInteractionHandler(this), this);

            getLogger().info("全息排行榜管理器初始化完成");
        } else {
            getLogger().warning("未检测到DecentHolograms插件，将使用传统NPC功能");
        }

        // 初始化门NPC管理器（兼容旧版本）
        this.doorNPCManager = new DoorNPCManager(this);

        // 初始化窗户管理器
        this.windowManager = new WindowManager(this);

        // 初始化动作条管理器
        this.actionBarManager = new ActionBarManager(this);
        this.actionBarManager.startUpdateTask();

        // 初始化敌对AI管理器
        this.hostileAIManager = new HostileAIManager(this);
        getLogger().info("敌对AI管理器初始化完成");

        // 创建命令管理器
        CommandManager commandManager = new CommandManager(this);

        // 注册命令
        PluginCommand command = getCommand("dzs");
        if (command != null) {
            command.setExecutor(commandManager);
            command.setTabCompleter(new CommandTabCompleter(this));
        }

        // 注册CZM命令
        PluginCommand czmCommand = getCommand("czm");
        if (czmCommand != null) {
            czmCommandExecutor = new CZMCommandExecutor(this);
            czmCommand.setExecutor(czmCommandExecutor);
            czmCommand.setTabCompleter(new CZMTabCompleter(this));
        }

        // 注册监听器
        getServer().getPluginManager().registerEvents(new DoorInteractionListener(this, doorManager), this);
        getServer().getPluginManager().registerEvents(regionSelector, this);
        getServer().getPluginManager().registerEvents(new PlayerStatusListener(this), this);

        // 创建并注册僵尸攻击监听器
        this.zombieAttackListener = new ZombieAttackListener(this);
        getServer().getPluginManager().registerEvents(zombieAttackListener, this);

        // 创建并注册子弹命中监听器
        this.bulletHitListener = new BulletHitListener(this);
        getServer().getPluginManager().registerEvents(bulletHitListener, this);

        // 创建并注册玩家死亡复活监听器
        this.playerDeathReviveListener = new PlayerDeathReviveListener(this);
        getServer().getPluginManager().registerEvents(playerDeathReviveListener, this);

        // 创建并注册玩家PvP监听器
        PlayerPvPListener playerPvPListener = new PlayerPvPListener(this);
        getServer().getPluginManager().registerEvents(playerPvPListener, this);

        // 设置相互引用
        zombieAttackListener.setDeathReviveListener(playerDeathReviveListener);
        playerPvPListener.setDeathReviveListener(playerDeathReviveListener);

        // 注册窗户修复监听器
        getServer().getPluginManager().registerEvents(new WindowRepairListener(this), this);

        // 注册玩家交互管理器作为事件监听器
        getServer().getPluginManager().registerEvents(playerInteractionManager, this);

        // 注册全局电源按钮交互监听器
        getServer().getPluginManager().registerEvents(new PowerButtonInteractionListener(this), this);

        // 注册物品栏移动限制监听器
        getServer().getPluginManager().registerEvents(new InventoryMoveListener(this), this);

        // 注册方块放置监听器（阻止玩家放置玻璃板和白色染料）
        getServer().getPluginManager().registerEvents(new BlockPlaceListener(this), this);

        // 注册物品消耗监听器
        getServer().getPluginManager().registerEvents(new ItemConsumeListener(this), this);

        // 注册幸运箱交互监听器
        getServer().getPluginManager().registerEvents(new LuckyBoxInteractionListener(this), this);

        // 注册幸运箱点击事件监听器
        luckyBoxManager.registerDecentHologramsListener();

        // 注册玩家显示设置同步监听器
        getServer().getPluginManager().registerEvents(new PlayerDisplaySyncListener(this), this);

        // 注册实体掉落物监听器（优先级最高，确保游戏实体不掉落物品）
        getServer().getPluginManager().registerEvents(new EntityDropListener(this), this);

        // 注册实体名称动态显示监听器
        this.entityNameDisplayListener = new org.Ver_zhzh.deathZombieV4.listeners.EntityNameDisplayListener(this);
        getServer().getPluginManager().registerEvents(entityNameDisplayListener, this);

        // 注册敌对AI监听器
        this.hostileAIListener = new HostileAIListener(this, hostileAIManager);
        getServer().getPluginManager().registerEvents(hostileAIListener, this);
        getLogger().info("敌对AI监听器已注册");

        // 注册UserCustomEntity事件监听器
        boolean debugMode = getConfig().getBoolean("debug", false); // 从配置中获取debug状态，默认false
        org.Ver_zhzh.customZombie.UserCustomEntity.UserCustomEntityEventListener userCustomEntityEventListener =
            new org.Ver_zhzh.customZombie.UserCustomEntity.UserCustomEntityEventListener(this, debugMode);
        getServer().getPluginManager().registerEvents(userCustomEntityEventListener, this);
        getLogger().info("UserCustomEntity事件监听器已注册");

        // 显示插件启动信息
        displayStartupBanner();

        // 启动Web服务器
        if (getConfig().getBoolean("web.enabled", false)) {
            int port = getConfig().getInt("web.port", 8081);
            String host = getConfig().getString("web.host", "");

            webServer = new WebServer(this);
            boolean started = webServer.start(port, host);

            if (started) {
                getLogger().info("Web服务器启动成功");
            } else {
                getLogger().severe("Web服务器启动失败！");
            }
        }

        // 延迟同步显示设置到Shoot插件（确保Shoot插件已完全加载）
        getServer().getScheduler().runTaskLater(this, () -> {
            if (playerDisplaySettingsManager != null) {
                playerDisplaySettingsManager.syncAllPlayersToShoot();
            }
        }, 20L); // 延迟1秒
    }

    /**
     * 显示插件启动横幅
     * 使用System.out.println直接输出到控制台，不写入日志文件
     */
    private void displayStartupBanner() {
        System.out.println("========================================================");
        System.out.println("                DeathZombieV4 插件启动中...");
        System.out.println("========================================================");
        System.out.println("");
        System.out.println("    ####  #####   ###  ##### #   #");
        System.out.println("    #   # #      #   #   #   #   #");
        System.out.println("    #   # ###    #####   #   #####");
        System.out.println("    #   # #      #   #   #   #   #");
        System.out.println("    ####  ##### #   #   #   #   #");
        System.out.println("");
        System.out.println("    ##### ##### #   # ##### ##### #####");
        System.out.println("      #   #   # ## ## #   #   #   #");
        System.out.println("      #   #   # # # # ##### ###   ###");
        System.out.println("      #   #   # #   # #   #   #   #");
        System.out.println("      #   ##### #   # ##### ##### #####");
        System.out.println("");
        System.out.println("                    V4 版本");
        System.out.println("");
        System.out.println("========================================================");
        System.out.println("  插件信息:");
        System.out.println("    版本: 1.2");
        System.out.println("    作者: Ver_zhzh");
        System.out.println("");
        System.out.println("  核心功能:");
        System.out.println("    + 多种僵尸类型支持");
        System.out.println("    + 回合制游戏系统");
        System.out.println("    + 实时计分板显示");
        System.out.println("    + 金钱奖励系统");
        System.out.println("    + 购买点与幸运箱");
        System.out.println("    + 门锁与电源系统");
        System.out.println("    + Web管理界面");
        System.out.println("");
        System.out.println("========================================================");
        System.out.println("              DeathZombieV4 插件启动完成！");
        System.out.println("========================================================");

        // 只在日志中记录简单的启动信息
        getLogger().info("DeathZombieV4 v1.2 插件已成功启动！");
    }

    @Override
    public void onDisable() {
        getLogger().info("正在关闭DeathZombieV4插件...");

        // 强制结束所有正在运行的游戏
        if (gameSessionManager != null) {
            getLogger().info("正在强制结束所有正在运行的游戏...");

            // 获取所有正在运行的游戏
            Set<String> runningGames = new HashSet<>(gameSessionManager.getRunningGames());

            // 强制结束每个游戏
            for (String gameName : runningGames) {
                getLogger().info("正在结束游戏: " + gameName);

                // 使用ZombieSpawnManager的endGameAndCleanup方法结束游戏并清理
                if (zombieSpawnManager != null) {
                    zombieSpawnManager.endGameAndCleanup(gameName);
                }

                // 直接调用GameSessionManager的endGame方法结束游戏
                gameSessionManager.endGame(gameName);

                getLogger().info("游戏 " + gameName + " 已成功结束");
            }

            // 停止所有游戏会话
            gameSessionManager.stopAllSessions();
            getLogger().info("所有游戏会话已停止");

            // 保存会话数据
            gameSessionManager.saveSessionData();
            getLogger().info("游戏会话数据已保存");
        }

        // 关闭Web服务器
        if (webServer != null) {
            webServer.stop();
            getLogger().info("Web服务器已关闭");
        }

        // 关闭计分板管理器
        if (scoreboardManager != null) {
            scoreboardManager.shutdown();
            getLogger().info("计分板管理器已关闭");
        }

        // 关闭僵尸生成管理器
        if (zombieSpawnManager != null) {
            zombieSpawnManager.shutdown();
            getLogger().info("僵尸生成管理器已关闭");
        }

        // 清理玩家交互数据
        if (playerInteractionManager != null) {
            getLogger().info("已清理玩家交互数据");
        }

        // 保存玩家显示设置
        if (playerDisplaySettingsManager != null) {
            playerDisplaySettingsManager.savePlayerSettings();
            getLogger().info("已保存所有玩家显示设置");
        }

        // 保存玩家统计数据
        if (playerStatisticsManager != null) {
            playerStatisticsManager.savePlayerStatistics();
            getLogger().info("已保存所有玩家统计数据");
        }

        if (zombieHelper != null) {
            getLogger().info("已清理僵尸生成辅助类资源");
        }

        // 清理门NPC
        if (doorNPCManager != null) {
            doorNPCManager.cleanupAllNPCs();
            getLogger().info("已清理所有门NPC");
        }

        // 清理悬浮文字
        if (hologramHelper != null) {
            hologramHelper.clearAllHolograms();
        }

        // 清理窗户资源
        if (windowManager != null) {
            // 遍历所有游戏，清理窗户资源
            for (String gameName : gameSessionManager.getRunningGames()) {
                windowManager.cleanupGame(gameName);
            }
            getLogger().info("已清理所有窗户资源");
        }

        // 清理玩家灵魂NPC
        if (playerDeathReviveListener != null) {
            playerDeathReviveListener.cleanupAllSoulNPCs();
            getLogger().info("已清理所有玩家灵魂NPC");
        }

        // 关闭动作条管理器
        if (actionBarManager != null) {
            actionBarManager.shutdown();
            getLogger().info("动作条管理器已关闭");
        }

        // 清理幸运箱管理器
        if (luckyBoxManager != null) {
            luckyBoxManager.clearAllDrawings();
            getLogger().info("幸运箱管理器已清理");
        }

        // 关闭日志管理器
        if (logManager != null) {
            logManager.shutdown();
            getLogger().info("日志管理器已关闭");
        }

        // 清理HologramHelper
        if (hologramHelper != null) {
            hologramHelper.clearAllHolograms();
        }

        // 清理DoorHologramManager
        if (doorHologramManager != null) {
            doorHologramManager.cleanupAllHolograms();
        }

        // 清理ShopHologramManager
        if (shopHologramManager != null) {
            shopHologramManager.cleanupAllHolograms();
        }

        // 清理PowerButtonEffectManager
        if (powerButtonEffectManager != null) {
            powerButtonEffectManager.cleanupAllEffects();
        }

        // 清理HologramLeaderboardManager
        if (hologramLeaderboardManager != null) {
            hologramLeaderboardManager.cleanup();
            getLogger().info("全息排行榜管理器已清理");
        }

        // 清理幸运箱资源
        if (luckyBoxManager != null) {
            luckyBoxManager.onDisable();
        }

        // 清理实体名称显示监听器
        if (entityNameDisplayListener != null) {
            entityNameDisplayListener.cleanup();
            getLogger().info("实体名称显示监听器已清理");
        }

        // 清理敌对AI管理器
        if (hostileAIManager != null) {
            hostileAIManager.cleanup();
            getLogger().info("敌对AI管理器已清理");
        }

        // 关闭IDZ系统
        if (czmCommandExecutor != null) {
            czmCommandExecutor.shutdown();
            getLogger().info("IDZ独立怪物系列已关闭");
        }

        // 发送关闭消息
        getLogger().info("DeathZombieV4 插件已禁用！");
    }

    /**
     * 获取游戏管理器
     *
     * @return GameManager实例
     */
    public GameManager getGameManager() {
        return gameManager;
    }

    /**
     * 获取游戏会话管理器
     *
     * @return GameSessionManager实例
     */
    public GameSessionManager getGameSessionManager() {
        return gameSessionManager;
    }

    /**
     * 获取门管理器
     *
     * @return DoorManager实例
     */
    public DoorManager getDoorManager() {
        return doorManager;
    }

    /**
     * 获取窗户管理器
     *
     * @return WindowManager实例
     */
    public WindowManager getWindowManager() {
        return windowManager;
    }

    /**
     * 获取区域选择器
     *
     * @return RegionSelector实例
     */
    public RegionSelector getRegionSelector() {
        return regionSelector;
    }

    /**
     * 获取计分板管理器
     *
     * @return ScoreboardManager实例
     */
    public ScoreboardManager getScoreboardManager() {
        return scoreboardManager;
    }

    /**
     * 获取Shoot插件辅助工具
     *
     * @return ShootPluginHelper实例
     */
    public ShootPluginHelper getShootPluginHelper() {
        return shootPluginHelper;
    }

    /**
     * 获取僵尸生成辅助工具
     *
     * @return ZombieHelper实例
     */
    public ZombieHelper getZombieHelper() {
        return zombieHelper;
    }

    /**
     * 获取僵尸生成管理器
     *
     * @return ZombieSpawnManager实例
     */
    public ZombieSpawnManager getZombieSpawnManager() {
        return zombieSpawnManager;
    }

    /**
     * 获取玩家交互管理器
     *
     * @return PlayerInteractionManager实例
     */
    public PlayerInteractionManager getPlayerInteractionManager() {
        return playerInteractionManager;
    }

    /**
     * 获取怪物ID映射器
     *
     * @return MonsterIdMapper实例
     */
    public MonsterIdMapper getMonsterIdMapper() {
        return monsterIdMapper;
    }

    /**
     * 获取Shoot插件实例
     *
     * @return Shoot插件实例，如果未找到则返回null
     */
    public Plugin getShootPlugin() {
        return shootPlugin;
    }

    /**
     * 获取门NPC管理器
     *
     * @return DoorNPCManager实例
     */
    public DoorNPCManager getDoorNPCManager() {
        return doorNPCManager;
    }

    /**
     * 获取悬浮文字助手
     *
     * @return HologramHelper实例
     */
    public HologramHelper getHologramHelper() {
        return hologramHelper;
    }

    /**
     * 获取门悬浮文字管理器
     *
     * @return DoorHologramManager实例
     */
    public DoorHologramManager getDoorHologramManager() {
        return doorHologramManager;
    }

    /**
     * 获取购买点悬浮文字管理器
     *
     * @return ShopHologramManager实例
     */
    public ShopHologramManager getShopHologramManager() {
        return shopHologramManager;
    }

    /**
     * 检查原生Title功能是否可用（始终返回true，因为现代服务端都支持）
     *
     * @return 始终返回true
     * @deprecated 现代服务端原生支持Title功能，此方法保留用于兼容性
     */
    @Deprecated
    public boolean isTitleAPIAvailable() {
        return true; // 现代服务端都原生支持Title功能
    }

    /**
     * 检查DecentHolograms插件是否可用
     *
     * @return 如果DecentHolograms插件可用返回true，否则返回false
     */
    public boolean isDecentHologramsAvailable() {
        return getServer().getPluginManager().getPlugin("DecentHolograms") != null;
    }

    /**
     * 获取玩家死亡复活监听器
     *
     * @return PlayerDeathReviveListener实例
     */
    public PlayerDeathReviveListener getPlayerDeathReviveListener() {
        return playerDeathReviveListener;
    }

    /**
     * 获取僵尸攻击监听器
     *
     * @return ZombieAttackListener实例
     */
    public ZombieAttackListener getZombieAttackListener() {
        return zombieAttackListener;
    }

    /**
     * 获取动作条管理器
     *
     * @return ActionBarManager实例
     */
    public ActionBarManager getActionBarManager() {
        return actionBarManager;
    }

    /**
     * 获取游戏装备管理器
     *
     * @return GameKitManager实例
     */
    public GameKitManager getGameKitManager() {
        return gameKitManager;
    }

    /**
     * 获取大厅管理器
     *
     * @return LobbyManager实例
     */
    public LobbyManager getLobbyManager() {
        return lobbyManager;
    }

    /**
     * 获取幸运箱管理器
     *
     * @return LuckyBoxManager实例
     */
    public LuckyBoxManager getLuckyBoxManager() {
        return luckyBoxManager;
    }

    /**
     * 获取日志管理器
     *
     * @return GlobalLogManager实例
     */
    public GlobalLogManager getLogManager() {
        return logManager;
    }

    /**
     * 获取玩家显示设置管理器
     *
     * @return PlayerDisplaySettingsManager实例
     */
    public PlayerDisplaySettingsManager getPlayerDisplaySettingsManager() {
        return playerDisplaySettingsManager;
    }

    /**
     * 获取玩家统计数据管理器
     *
     * @return PlayerStatisticsManager实例
     */
    public PlayerStatisticsManager getPlayerStatisticsManager() {
        return playerStatisticsManager;
    }

    /**
     * 获取CustomZombie实例
     *
     * @return CustomZombie实例，如果未初始化则返回null
     */
    public org.Ver_zhzh.customZombie.CustomZombie getCustomZombie() {
        return customZombie;
    }

    /**
     * 获取双CustomZombie系统管理器
     *
     * @return 双系统管理器实例，如果未初始化则返回null
     */
    public org.Ver_zhzh.customZombie.DualZombieSystemManager getDualZombieSystemManager() {
        if (zombieHelper != null) {
            return zombieHelper.getDualZombieSystemManager();
        }
        return null;
    }

    /**
     * 获取批量死亡检测器
     *
     * @return BatchDeathDetector实例
     */
    public BatchDeathDetector getBatchDeathDetector() {
        return batchDeathDetector;
    }

    /**
     * 检查并重置所有处于ENDED状态的游戏 在插件启动时调用，将所有ENDED状态的游戏重置为WAITING状态
     */
    private void resetEndedGames() {
        if (gameSessionManager == null) {
            getLogger().warning("游戏会话管理器未初始化，无法重置游戏状态");
            return;
        }

        // 获取所有游戏及其状态
        Map<String, GameSessionManager.GameState> games = gameSessionManager.getRunningGamesWithStates();
        int resetCount = 0;

        for (Map.Entry<String, GameSessionManager.GameState> entry : games.entrySet()) {
            String gameName = entry.getKey();
            GameSessionManager.GameState state = entry.getValue();

            // 检查游戏是否处于ENDED状态
            if (state == GameSessionManager.GameState.ENDED) {
                // 检查游戏是否启用
                if (gameManager.isGameEnabled(gameName)) {
                    // 重置游戏状态为WAITING
                    boolean success = gameSessionManager.resetGame(gameName);
                    if (success) {
                        getLogger().info("已将游戏 '" + gameName + "' 从ENDED状态重置为WAITING状态");
                        resetCount++;
                    } else {
                        getLogger().warning("重置游戏 '" + gameName + "' 状态失败");
                    }
                } else {
                    getLogger().info("游戏 '" + gameName + "' 处于ENDED状态但未启用，跳过重置");
                }
            }
        }

        if (resetCount > 0) {
            getLogger().info("插件启动时共重置了 " + resetCount + " 个已结束的游戏");
        } else {
            getLogger().info("没有需要重置的已结束游戏");
        }

        // 重置所有游戏的生成点状态
        resetAllSpawnPointStates();
    }

    /**
     * 重置所有游戏的生成点状态
     */
    private void resetAllSpawnPointStates() {
        getLogger().info("正在重置所有游戏的生成点状态...");

        // 获取所有游戏
        Set<String> allGames = gameManager.getAllGames();
        int processedGames = 0;

        for (String gameName : allGames) {
            try {
                getLogger().info("正在处理游戏: " + gameName);

                // 获取所有生成点（不管游戏是否启用）
                Map<String, Map<String, Object>> spawns = gameManager.getZombieSpawns(gameName);
                if (spawns != null && !spawns.isEmpty()) {
                    getLogger().info("游戏 " + gameName + " 有 " + spawns.size() + " 个生成点需要重置");

                    // 首先将所有生成点设置为禁用（默认状态）
                    for (String spawnName : spawns.keySet()) {
                        boolean setResult = gameManager.setZombieSpawnEnabled(gameName, spawnName, false);
                        getLogger().info("将生成点 " + spawnName + " 设置为禁用: " + (setResult ? "成功" : "失败"));
                    }

                    // 然后根据门锁状态重新设置生成点状态
                    for (String spawnName : spawns.keySet()) {
                        boolean shouldEnable = false; // 默认禁用

                        // 获取与该生成点关联的所有门
                        List<String> linkedDoors = doorManager.getLinkedDoors(gameName, spawnName);

                        if (linkedDoors.isEmpty()) {
                            // 如果没有关联的门，保持禁用状态
                            getLogger().info("生成点 " + spawnName + " 没有关联的门，保持禁用状态");
                        } else {
                            // 检查是否有任何关联的门已解锁
                            boolean anyDoorUnlocked = false;
                            for (String doorName : linkedDoors) {
                                boolean isUnlocked = doorManager.isDoorUnlocked(gameName, doorName);
                                getLogger().info("生成点 " + spawnName + " 关联的门 " + doorName + " 解锁状态: " + isUnlocked);
                                if (isUnlocked) {
                                    anyDoorUnlocked = true;
                                    break;
                                }
                            }

                            if (anyDoorUnlocked) {
                                shouldEnable = true;
                                getLogger().info("生成点 " + spawnName + " 至少有一个关联门已解锁，设置为启用");
                            } else {
                                getLogger().info("生成点 " + spawnName + " 的所有关联门都未解锁，保持禁用");
                            }
                        }

                        // 设置生成点状态
                        boolean setResult = gameManager.setZombieSpawnEnabled(gameName, spawnName, shouldEnable);
                        getLogger().info("设置生成点 " + spawnName + " 为 " + (shouldEnable ? "启用" : "禁用") + ": " + (setResult ? "成功" : "失败"));
                    }

                    processedGames++;
                    getLogger().info("已重置游戏 " + gameName + " 的 " + spawns.size() + " 个生成点状态");
                } else {
                    getLogger().info("游戏 " + gameName + " 没有设置任何生成点，跳过");
                }
            } catch (Exception e) {
                getLogger().warning("重置游戏 " + gameName + " 的生成点状态时出错: " + e.getMessage());
                e.printStackTrace();
            }
        }

        getLogger().info("已完成 " + processedGames + " 个游戏的生成点状态重置");
    }

    /**
     * 获取全局电源按钮效果管理器
     *
     * @return PowerButtonEffectManager实例
     */
    public PowerButtonEffectManager getPowerButtonEffectManager() {
        return powerButtonEffectManager;
    }

    /**
     * 获取实体名称显示监听器
     *
     * @return EntityNameDisplayListener实例
     */
    public org.Ver_zhzh.deathZombieV4.listeners.EntityNameDisplayListener getEntityNameDisplayListener() {
        return entityNameDisplayListener;
    }

    /**
     * 获取敌对AI管理器
     *
     * @return HostileAIManager实例
     */
    public HostileAIManager getHostileAIManager() {
        return hostileAIManager;
    }

    /**
     * 获取消息管理器
     *
     * @return MessageManager实例
     */
    public MessageManager getMessageManager() {
        return messageManager;
    }

    /**
     * 获取初始装备编辑器GUI
     *
     * @return KitEditorGUI实例
     */
    public KitEditorGUI getKitEditorGUI() {
        return kitEditorGUI;
    }

    /**
     * 获取全息排行榜管理器
     *
     * @return HologramLeaderboardManager实例
     */
    public HologramLeaderboardManager getHologramLeaderboardManager() {
        return hologramLeaderboardManager;
    }
}
